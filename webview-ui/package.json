{"name": "webview-ui", "version": "0.3.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:test": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .ts,.tsx", "test": "vitest run", "test:watch": "vitest dev", "test:coverage": "vitest run --coverage", "devtools": "react-devtools"}, "dependencies": {"@floating-ui/react": "^0.27.4", "@heroui/react": "^2.8.0-beta.2", "@vscode/webview-ui-toolkit": "^1.4.0", "debounce": "^2.1.1", "dompurify": "^3.2.4", "fast-deep-equal": "^3.1.3", "firebase": "^11.3.0", "framer-motion": "^12.7.4", "fuse.js": "^7.0.0", "fzf": "^0.5.2", "katex": "^0.16.22", "lucide-react": "^0.511.0", "mermaid": "^11.4.1", "posthog-js": "^1.224.0", "pretty-bytes": "^6.1.1", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-remark": "^2.1.0", "react-textarea-autosize": "^8.5.7", "react-use": "^17.6.0", "react-virtuoso": "^4.12.3", "rehype-highlight": "^7.0.1", "rehype-katex": "^7.0.1", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "remark-math": "^6.0.0", "remark-stringify": "^11.0.0", "styled-components": "^6.1.15", "unified": "^11.0.5", "uuid": "^9.0.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tailwindcss/vite": "^4.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.14", "@types/katex": "^0.16.7", "@types/node": "^22.13.4", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/uuid": "^9.0.8", "@types/vscode-webview": "^1.57.5", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.0.9", "eslint": "^8.57.0", "eslint-plugin-eslint-rules": "file:../eslint-rules", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "jsdom": "^26.0.0", "react-devtools": "^6.1.2", "tailwindcss": "^4.1.5", "typescript": "^5.7.3", "typescript-eslint": "^8.18.2", "vite": "^6.3.4", "vitest": "^3.0.5"}, "optionalDependencies": {"@rollup/rollup-linux-arm64-gnu": "^4.40.0", "@rollup/rollup-linux-x64-gnu": "^4.40.0", "@rollup/rollup-win32-x64-msvc": "^4.40.0", "@swc/core-linux-x64-gnu": "^1.11.0", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1", "lightningcss-win32-x64-msvc": "1.29.2"}}