import { useExtensionState } from "@/context/ExtensionStateContext"
import { VSCodeButton, VSCodeCheckbox, VSCodeTextField, VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"
import { useState, useEffect } from "react"
import Section from "./Section"
import SectionHeader from "./SectionHeader"
import { ApiKeyField } from "./common/ApiKeyField"
import { BaseUrlField } from "./common/BaseUrlField"
import { ErrorMessage } from "./common/ErrorMessage"
import { DropdownContainer } from "./common/ModelSelector"

interface AutocompleteSettingsSectionProps {
	// No props needed for now
}

export default function AutocompleteSettingsSection(_props: AutocompleteSettingsSectionProps) {
	const { autocompleteSettings, setAutocompleteSettings } = useExtensionState()
	const [localSettings, setLocalSettings] = useState(autocompleteSettings)
	const [errors, setErrors] = useState<string[]>([])

	// Update local settings when global settings change
	useEffect(() => {
		setLocalSettings(autocompleteSettings)
	}, [autocompleteSettings])

	const handleInputChange = (field: string) => (event: any) => {
		const value = event.target?.value ?? event.detail?.value ?? event

		if (field.startsWith("fim.")) {
			const fimField = field.split(".")[1] as keyof NonNullable<typeof localSettings.fim>
			setLocalSettings((prev) => ({
				...prev,
				fim: {
					...prev.fim,
					[fimField]: value,
				},
			}))
		} else {
			setLocalSettings((prev) => ({
				...prev,
				[field]: value,
			}))
		}
	}

	const handleCheckboxChange = (field: keyof typeof localSettings) => (event: any) => {
		const checked = event.target?.checked ?? event.detail?.checked ?? event
		setLocalSettings((prev) => ({
			...prev,
			[field]: checked,
		}))
	}

	const handleSave = async () => {
		// Validate settings
		const validationErrors: string[] = []

		if (localSettings.enabled && !localSettings.apiKey) {
			validationErrors.push("API key is required when autocomplete is enabled")
		}

		if (localSettings.apiBaseUrl && !isValidUrl(localSettings.apiBaseUrl)) {
			validationErrors.push("Invalid API base URL")
		}

		if (localSettings.maxTokens && (localSettings.maxTokens < 1 || localSettings.maxTokens > 10000)) {
			validationErrors.push("Max tokens must be between 1 and 10000")
		}

		if (localSettings.temperature && (localSettings.temperature < 0 || localSettings.temperature > 2)) {
			validationErrors.push("Temperature must be between 0 and 2")
		}

		if (
			localSettings.requestTimeoutMs &&
			(localSettings.requestTimeoutMs < 1000 || localSettings.requestTimeoutMs > 300000)
		) {
			validationErrors.push("Request timeout must be between 1000ms and 300000ms")
		}

		setErrors(validationErrors)

		if (validationErrors.length === 0) {
			setAutocompleteSettings(localSettings)
		}
	}

	const handleReset = () => {
		setLocalSettings(autocompleteSettings)
		setErrors([])
	}

	const isValidUrl = (url: string): boolean => {
		try {
			new URL(url)
			return true
		} catch {
			return false
		}
	}

	const hasChanges = JSON.stringify(localSettings) !== JSON.stringify(autocompleteSettings)

	return (
		<Section>
			<SectionHeader description="Configure QAX autocomplete settings for intelligent code completion">
				QAX Autocomplete Configuration
			</SectionHeader>

			{errors.length > 0 && (
				<div style={{ marginBottom: "16px" }}>
					{errors.map((error, index) => (
						<ErrorMessage key={index} message={error} />
					))}
				</div>
			)}

			<div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
				<VSCodeCheckbox checked={localSettings.enabled} onChange={handleCheckboxChange("enabled")}>
					Enable QAX Autocomplete
				</VSCodeCheckbox>

				<DropdownContainer className="dropdown-container">
					<label htmlFor="autocomplete-provider">
						<span style={{ fontWeight: 500 }}>Provider</span>
					</label>
					<VSCodeDropdown
						id="autocomplete-provider"
						value={localSettings.provider || "openai"}
						onChange={handleInputChange("provider")}
						style={{ width: "100%" }}>
						<VSCodeOption value="openai">OpenAI Compatible</VSCodeOption>
						<VSCodeOption value="fim">FIM (Fill in the Middle)</VSCodeOption>
					</VSCodeDropdown>
				</DropdownContainer>

				{localSettings.provider === "fim" ? (
					<>
						<ApiKeyField
							value={localSettings.fim?.apiKey || ""}
							onChange={handleInputChange("fim.apiKey")}
							providerName="FIM API"
							placeholder="Enter your FIM API key..."
						/>

						<BaseUrlField
							value={localSettings.fim?.baseUrl || ""}
							onChange={handleInputChange("fim.baseUrl")}
							placeholder="https://your-fim-api.com/v1"
							label="FIM Base URL"
						/>
					</>
				) : (
					<>
						<ApiKeyField
							value={localSettings.apiKey || ""}
							onChange={handleInputChange("apiKey")}
							providerName="OpenAI Compatible API"
							placeholder="Enter your API key..."
						/>

						<BaseUrlField
							value={localSettings.apiBaseUrl || ""}
							onChange={handleInputChange("apiBaseUrl")}
							placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
							label="API Base URL"
						/>
					</>
				)}

				<VSCodeTextField
					value={localSettings.modelId || ""}
					style={{ width: "100%" }}
					onInput={handleInputChange("modelId")}
					placeholder="google/gemini-2.5-flash-preview-05-20">
					<span style={{ fontWeight: 500 }}>Model ID</span>
				</VSCodeTextField>

				{/* 数字选项方格布局 */}
				<div style={{
					display: "grid",
					gridTemplateColumns: "repeat(auto-fit, minmax(220px, 1fr))",
					gap: "16px",
					marginTop: "8px",
					padding: "12px",
					backgroundColor: "var(--vscode-editor-background)",
					border: "1px solid var(--vscode-input-border)",
					borderRadius: "4px"
				}}>
					<VSCodeTextField
						value={localSettings.maxTokens?.toString() || ""}
						style={{ width: "100%" }}
						onInput={(e) => handleInputChange("maxTokens")(parseInt((e.target as HTMLInputElement)?.value) || 1000)}
						placeholder="1000">
						<span style={{ fontWeight: 500 }}>Max Tokens</span>
					</VSCodeTextField>

					<VSCodeTextField
						value={localSettings.temperature?.toString() || ""}
						style={{ width: "100%" }}
						onInput={(e) => handleInputChange("temperature")(parseFloat((e.target as HTMLInputElement)?.value) || 0.1)}
						placeholder="0.1">
						<span style={{ fontWeight: 500 }}>Temperature</span>
					</VSCodeTextField>

					<VSCodeTextField
						value={localSettings.requestTimeoutMs?.toString() || ""}
						style={{ width: "100%" }}
						onInput={(e) =>
							handleInputChange("requestTimeoutMs")(parseInt((e.target as HTMLInputElement)?.value) || 30000)
						}
						placeholder="30000">
						<span style={{ fontWeight: 500 }}>Request Timeout (ms)</span>
					</VSCodeTextField>

					<VSCodeTextField
						value={localSettings.debounceMs?.toString() || ""}
						style={{ width: "100%" }}
						onInput={(e) => handleInputChange("debounceMs")(parseInt((e.target as HTMLInputElement)?.value) || 300)}
						placeholder="300">
						<span style={{ fontWeight: 500 }}>Debounce Time (ms)</span>
					</VSCodeTextField>
				</div>

				<VSCodeCheckbox checked={localSettings.usePromptCache || false} onChange={handleCheckboxChange("usePromptCache")}>
					Use Prompt Cache
				</VSCodeCheckbox>

				{hasChanges && (
					<div style={{ display: "flex", gap: "8px", marginTop: "16px" }}>
						<VSCodeButton onClick={handleSave}>Save Settings</VSCodeButton>
						<VSCodeButton appearance="secondary" onClick={handleReset}>
							Reset
						</VSCodeButton>
					</div>
				)}
			</div>

			<div
				style={{
					marginTop: "24px",
					padding: "12px",
					backgroundColor: "var(--vscode-textBlockQuote-background)",
					border: "1px solid var(--vscode-textBlockQuote-border)",
					borderRadius: "4px",
				}}>
				<p
					style={{
						margin: 0,
						fontSize: "12px",
						color: "var(--vscode-descriptionForeground)",
					}}>
					<strong>Note:</strong> QAX Autocomplete provides intelligent code completion using AI models. Choose between:
					<br />
					<strong>OpenAI Compatible:</strong> Works with OpenRouter, OpenAI, Azure OpenAI, local models (Ollama, LM
					Studio), and other OpenAI-compatible services.
					<br />
					<strong>FIM (Fill in the Middle):</strong> Specialized API format that sends both prefix and suffix context
					for more accurate completions.
					<br />
					Configure your provider settings to enable intelligent code suggestions based on your current context.
				</p>
			</div>
		</Section>
	)
}
