import * as vscode from "vscode"
import { AutocompleteSettings, DEFAULT_AUTOCOMPLETE_SETTINGS } from "../../shared/AutocompleteSettings"

/**
 * Configuration manager for autocomplete settings
 * Replaces the ContextProxy dependency from the original autocomplete module
 */
export class AutocompleteConfigManager {
	private static _instance: AutocompleteConfigManager | null = null
	private _context: vscode.ExtensionContext
	private _settings: AutocompleteSettings

	private constructor(context: vscode.ExtensionContext) {
		this._context = context
		this._settings = this.loadSettings()
	}

	public static initialize(context: vscode.ExtensionContext): void {
		if (!AutocompleteConfigManager._instance) {
			AutocompleteConfigManager._instance = new AutocompleteConfigManager(context)
		}
	}

	public static get instance(): AutocompleteConfigManager {
		if (!AutocompleteConfigManager._instance) {
			throw new Error("AutocompleteConfigManager must be initialized before use")
		}
		return AutocompleteConfigManager._instance
	}

	/**
	 * Load autocomplete settings from VS Code configuration and global state
	 */
	private loadSettings(): AutocompleteSettings {
		const config = vscode.workspace.getConfiguration("cline.autocomplete")
		const globalState = this._context.globalState

		return {
			enabled: config.get("enabled", DEFAULT_AUTOCOMPLETE_SETTINGS.enabled),
			provider: config.get("provider", DEFAULT_AUTOCOMPLETE_SETTINGS.provider),
			apiKey: globalState.get("autocompleteApiKey") || config.get("apiKey"),
			apiBaseUrl: config.get("apiBaseUrl", DEFAULT_AUTOCOMPLETE_SETTINGS.apiBaseUrl),
			modelId: config.get("modelId", DEFAULT_AUTOCOMPLETE_SETTINGS.modelId),
			maxTokens: config.get("maxTokens", DEFAULT_AUTOCOMPLETE_SETTINGS.maxTokens),
			temperature: config.get("temperature", DEFAULT_AUTOCOMPLETE_SETTINGS.temperature),
			requestTimeoutMs: config.get("requestTimeoutMs", DEFAULT_AUTOCOMPLETE_SETTINGS.requestTimeoutMs),
			usePromptCache: config.get("usePromptCache", DEFAULT_AUTOCOMPLETE_SETTINGS.usePromptCache),
			customHeaders: config.get("customHeaders", DEFAULT_AUTOCOMPLETE_SETTINGS.customHeaders),
			debounceMs: config.get("debounceMs", DEFAULT_AUTOCOMPLETE_SETTINGS.debounceMs),
			fim: {
				apiKey:
					globalState.get("autocompleteFimApiKey") ||
					config.get("fim.apiKey", DEFAULT_AUTOCOMPLETE_SETTINGS.fim?.apiKey),
				baseUrl: config.get("fim.baseUrl", DEFAULT_AUTOCOMPLETE_SETTINGS.fim?.baseUrl),
			},
		}
	}

	/**
	 * Get current autocomplete settings
	 */
	public getSettings(): AutocompleteSettings {
		// Reload settings to get latest values
		this._settings = this.loadSettings()
		return { ...this._settings }
	}

	/**
	 * Update autocomplete settings
	 */
	public async updateSettings(settings: Partial<AutocompleteSettings>): Promise<void> {
		const config = vscode.workspace.getConfiguration("cline.autocomplete")
		const globalState = this._context.globalState

		// Update configuration values
		if (settings.enabled !== undefined) {
			await config.update("enabled", settings.enabled, vscode.ConfigurationTarget.Global)
		}
		if (settings.provider !== undefined) {
			await config.update("provider", settings.provider, vscode.ConfigurationTarget.Global)
		}
		if (settings.apiKey !== undefined) {
			await globalState.update("autocompleteApiKey", settings.apiKey)
		}
		if (settings.apiBaseUrl !== undefined) {
			await config.update("apiBaseUrl", settings.apiBaseUrl, vscode.ConfigurationTarget.Global)
		}
		if (settings.modelId !== undefined) {
			await config.update("modelId", settings.modelId, vscode.ConfigurationTarget.Global)
		}
		if (settings.maxTokens !== undefined) {
			await config.update("maxTokens", settings.maxTokens, vscode.ConfigurationTarget.Global)
		}
		if (settings.temperature !== undefined) {
			await config.update("temperature", settings.temperature, vscode.ConfigurationTarget.Global)
		}
		if (settings.requestTimeoutMs !== undefined) {
			await config.update("requestTimeoutMs", settings.requestTimeoutMs, vscode.ConfigurationTarget.Global)
		}
		if (settings.usePromptCache !== undefined) {
			await config.update("usePromptCache", settings.usePromptCache, vscode.ConfigurationTarget.Global)
		}
		if (settings.customHeaders !== undefined) {
			await config.update("customHeaders", settings.customHeaders, vscode.ConfigurationTarget.Global)
		}
		if (settings.debounceMs !== undefined) {
			await config.update("debounceMs", settings.debounceMs, vscode.ConfigurationTarget.Global)
		}
		if (settings.fim?.apiKey !== undefined) {
			await globalState.update("autocompleteFimApiKey", settings.fim.apiKey)
		}
		if (settings.fim?.baseUrl !== undefined) {
			await config.update("fim.baseUrl", settings.fim.baseUrl, vscode.ConfigurationTarget.Global)
		}

		// Reload settings
		this._settings = this.loadSettings()
	}

	/**
	 * Get provider settings in the format expected by the original autocomplete code
	 * This method provides compatibility with the original ContextProxy.getProviderSettings() method
	 */
	public getProviderSettings(): { qaxToken?: string; qaxModel?: string; qaxBaseUrl?: string } {
		const settings = this.getSettings()
		return {
			qaxToken: settings.apiKey,
			qaxModel: settings.modelId,
			qaxBaseUrl: settings.apiBaseUrl,
		}
	}

	/**
	 * Get global state value
	 * This method provides compatibility with the original ContextProxy.getGlobalState() method
	 */
	public getGlobalState(key: string): any {
		if (key === "experiments") {
			// Return autocomplete experiment flag based on enabled setting
			return {
				autocomplete: this.getSettings().enabled,
			}
		}
		return this._context.globalState.get(key)
	}

	/**
	 * Set global state value
	 */
	public async setGlobalState(key: string, value: any): Promise<void> {
		await this._context.globalState.update(key, value)
	}

	/**
	 * Check if autocomplete is enabled
	 */
	public isEnabled(): boolean {
		return this.getSettings().enabled
	}

	/**
	 * Check if API key is configured based on the selected provider
	 */
	public hasApiKey(): boolean {
		const settings = this.getSettings()

		if (settings.provider === "fim") {
			return !!(settings.fim?.apiKey && settings.fim.apiKey.trim() && settings.fim?.baseUrl && settings.fim.baseUrl.trim())
		} else {
			// Default to OpenAI provider
			return !!(settings.apiKey && settings.apiKey.trim())
		}
	}
}
