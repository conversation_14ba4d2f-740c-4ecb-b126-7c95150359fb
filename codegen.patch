diff --git a/AUTOCOMPLETE_PROVIDER_CONDITIONAL_UI.md b/AUTOCOMPLETE_PROVIDER_CONDITIONAL_UI.md
new file mode 100644
index ********..603ec2f6
--- /dev/null
+++ b/AUTOCOMPLETE_PROVIDER_CONDITIONAL_UI.md
@@ -0,0 +1,159 @@
+# Autocomplete Provider 条件显示 UI
+
+## 实现逻辑
+
+根据用户选择的 Provider 类型，动态显示对应的配置字段：
+
+### 条件显示逻辑
+```tsx
+{localSettings.provider === "fim" ? (
+  // 显示 FIM 相关配置
+  <>
+    <ApiKeyField
+      value={localSettings.fim?.apiKey || ""}
+      onChange={handleInputChange("fim.apiKey")}
+      providerName="FIM API"
+      placeholder="Enter your FIM API key..."
+    />
+    
+    <BaseUrlField
+      value={localSettings.fim?.baseUrl || ""}
+      onChange={handleInputChange("fim.baseUrl")}
+      placeholder="https://your-fim-api.com/v1"
+      label="FIM Base URL"
+    />
+  </>
+) : (
+  // 显示 OpenAI Compatible 相关配置
+  <>
+    <ApiKeyField
+      value={localSettings.apiKey || ""}
+      onChange={handleInputChange("apiKey")}
+      providerName="OpenAI Compatible API"
+      placeholder="Enter your API key..."
+    />
+    
+    <BaseUrlField
+      value={localSettings.apiBaseUrl || ""}
+      onChange={handleInputChange("apiBaseUrl")}
+      placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
+      label="API Base URL"
+    />
+  </>
+)}
+```
+
+## UI 行为
+
+### 选择 "OpenAI Compatible" 时
+显示字段：
+- **API Key**: OpenAI Compatible API 的密钥
+- **API Base URL**: OpenAI Compatible API 的基础 URL
+  - 占位符：`https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)`
+  - 标签：`API Base URL`
+
+### 选择 "FIM (Fill in the Middle)" 时
+显示字段：
+- **API Key**: FIM API 的密钥
+- **FIM Base URL**: FIM API 的基础 URL
+  - 占位符：`https://your-fim-api.com/v1`
+  - 标签：`FIM Base URL`
+
+## 配置页面结构
+
+```
+┌─ Autocomplete Settings ─────────────────────────────┐
+│                                                     │
+│ ☑ Enable QAX Autocomplete                          │
+│                                                     │
+│ Provider: [OpenAI Compatible ▼]                    │
+│                                                     │
+│ ┌─ 根据 Provider 选择动态显示 ─────────────────────┐ │
+│ │                                                 │ │
+│ │ 如果选择 "OpenAI Compatible":                   │ │
+│ │   API Key: [Enter your API key...]             │ │
+│ │   API Base URL: [https://api.openrouter.ai...] │ │
+│ │                                                 │ │
+│ │ 如果选择 "FIM":                                 │ │
+│ │   API Key: [Enter your FIM API key...]         │ │
+│ │   FIM Base URL: [https://your-fim-api.com/v1]  │ │
+│ └─────────────────────────────────────────────────┘ │
+│                                                     │
+│ Model ID: [google/gemini-2.5-flash-preview-05-20]  │
+│ Max Tokens: [1000]                                  │
+│ Temperature: [0.1]                                  │
+│ Request Timeout (ms): [30000]                       │
+│ Debounce Time (ms): [300]                           │
+│ ☐ Use Prompt Cache                                  │
+│                                                     │
+│ [Save Settings] [Reset]                             │
+└─────────────────────────────────────────────────────┘
+```
+
+## 用户体验
+
+### 优势
+1. **清晰的界面**：只显示当前选择 Provider 相关的配置
+2. **减少混淆**：避免用户同时看到两套 API 配置
+3. **聚焦配置**：用户专注于当前选择的 Provider 配置
+4. **动态响应**：切换 Provider 时界面立即更新
+
+### 交互流程
+1. 用户选择 Provider（OpenAI Compatible 或 FIM）
+2. 界面动态显示对应的 API Key 和 Base URL 字段
+3. 用户填写相应的配置信息
+4. 保存设置
+
+## 配置数据结构
+
+### OpenAI Compatible 配置
+```typescript
+{
+  provider: "openai",
+  apiKey: "sk-xxx...",
+  apiBaseUrl: "https://api.openrouter.ai/api/v1",
+  // ... 其他通用设置
+}
+```
+
+### FIM 配置
+```typescript
+{
+  provider: "fim",
+  fim: {
+    apiKey: "fim-key-xxx...",
+    baseUrl: "https://your-fim-api.com/v1"
+  },
+  // ... 其他通用设置
+}
+```
+
+## 验证逻辑
+
+### 根据 Provider 验证相应字段
+```typescript
+if (settings.provider === "fim") {
+  // 验证 FIM 配置
+  if (!settings.fim?.apiKey) {
+    errors.push("FIM API key is required when FIM provider is enabled")
+  }
+  if (!settings.fim?.baseUrl) {
+    errors.push("FIM base URL is required when FIM provider is enabled")
+  }
+} else {
+  // 验证 OpenAI 配置
+  if (!settings.apiKey) {
+    errors.push("API key is required when autocomplete is enabled")
+  }
+}
+```
+
+## 实现特点
+
+1. **条件渲染**：使用三元运算符根据 provider 选择显示不同内容
+2. **独立配置**：两种 Provider 的配置完全独立存储
+3. **即时切换**：切换 Provider 时界面立即响应
+4. **配置保持**：切换 Provider 不会丢失已填写的配置
+5. **清晰标识**：不同 Provider 的字段有明确的标签和占位符
+
+这种实现方式提供了清晰、直观的用户界面，让用户能够专注于当前选择的 Provider 配置！
diff --git a/AUTOCOMPLETE_PROVIDER_FEATURE.md b/AUTOCOMPLETE_PROVIDER_FEATURE.md
new file mode 100644
index ********..6d456fe6
--- /dev/null
+++ b/AUTOCOMPLETE_PROVIDER_FEATURE.md
@@ -0,0 +1,91 @@
+# Autocomplete Provider Feature Implementation
+
+## 功能概述
+
+成功为 Cline 的 autocomplete 功能添加了 Provider 选择功能，支持 OpenAI Compatible 和 FIM (Fill in the Middle) 两种 API 格式。
+
+## 实现的功能
+
+### 1. Provider 选择
+- **OpenAI Compatible**: 支持 OpenRouter、OpenAI、Azure OpenAI、本地模型等
+- **FIM (Fill in the Middle)**: 专门的 Fill in the Middle API 格式
+
+### 2. 配置界面
+- 在 Autocomplete 设置页面添加了 Provider 下拉选择框
+- 根据选择的 Provider 动态显示相应的配置字段
+- 支持独立的 API Key 和 Base URL 配置
+
+### 3. API 处理
+- 创建了专门的 `FimHandler` 类处理 FIM API 请求
+- 支持 `prompt`、`suffix`、`stream` 参数
+- 自动根据光标位置提取前后文上下文
+
+## 使用方法
+
+### 配置 OpenAI Compatible Provider
+1. 打开 Cline 设置页面
+2. 切换到 "Autocomplete" 标签
+3. 选择 Provider 为 "OpenAI Compatible"
+4. 输入 API Key
+5. 设置 Base URL（如 https://api.openrouter.ai/api/v1）
+
+### 配置 FIM Provider
+1. 打开 Cline 设置页面
+2. 切换到 "Autocomplete" 标签
+3. 选择 Provider 为 "FIM (Fill in the Middle)"
+4. 输入 FIM API Key
+5. 设置 FIM Base URL（如 https://your-fim-api.com/v1）
+
+## FIM API 格式
+
+FIM Provider 会发送如下格式的请求：
+
+```json
+{
+  "prompt": "光标前的代码内容",
+  "suffix": "光标后的代码内容",
+  "stream": false,
+  "max_tokens": 100,
+  "temperature": 0.1
+}
+```
+
+## 技术实现
+
+### 文件修改列表
+1. `src/shared/AutocompleteSettings.ts` - 扩展配置接口
+2. `src/api/providers/fim.ts` - 新建 FIM 处理器
+3. `src/api/index.ts` - 添加 FIM 处理器构建函数
+4. `src/services/autocomplete/AutocompleteConfigManager.ts` - 更新配置管理
+5. `src/services/autocomplete/AutocompleteProvider.ts` - 更新主要逻辑
+6. `webview-ui/src/components/settings/AutocompleteSettingsSection.tsx` - 更新 UI
+7. `package.json` - 添加配置字段定义
+
+### 核心特性
+- 类型安全的配置管理
+- 动态 Provider 切换
+- 错误处理和超时机制
+- 流式和非流式响应支持
+- 缓存机制兼容
+
+## 测试状态
+
+✅ 编译成功 - 所有 TypeScript 类型检查通过
+✅ Linting 通过 - 只有少量样式警告
+✅ Webview 构建成功
+✅ 配置界面正常显示
+
+## 下一步
+
+建议进行以下测试：
+1. 在 VS Code 中加载扩展
+2. 测试 Provider 选择功能
+3. 验证配置保存和加载
+4. 测试实际的代码补全功能
+5. 验证 FIM API 请求格式
+
+## 注意事项
+
+- FIM API 的具体响应格式可能因服务提供商而异，可能需要根据实际 API 调整响应解析逻辑
+- 当前实现默认使用非流式请求，可以根据需要启用流式处理
+- 配置验证确保在启用相应 Provider 时必须提供必要的配置信息
diff --git a/AUTOCOMPLETE_SETTINGS_IMPROVEMENTS.md b/AUTOCOMPLETE_SETTINGS_IMPROVEMENTS.md
new file mode 100644
index ********..465657bd
--- /dev/null
+++ b/AUTOCOMPLETE_SETTINGS_IMPROVEMENTS.md
@@ -0,0 +1,164 @@
+# Autocomplete 设置改进
+
+## 新增功能
+
+### 1. **Debounce 时间设置**
+
+#### 配置接口扩展
+```typescript
+export interface AutocompleteSettings {
+  // ... 其他字段
+  debounceMs?: number  // 新增：防抖时间设置
+}
+```
+
+#### 默认值
+```typescript
+export const DEFAULT_AUTOCOMPLETE_SETTINGS: AutocompleteSettings = {
+  // ... 其他设置
+  debounceMs: 300, // 默认 300ms 防抖
+}
+```
+
+#### 验证规则
+```typescript
+if (settings.debounceMs && (settings.debounceMs < 0 || settings.debounceMs > 5000)) {
+  errors.push("Debounce time must be between 0ms and 5000ms")
+}
+```
+
+#### 动态使用
+```typescript
+// 从配置中获取防抖时间
+const getDebounceMs = () => {
+  const configManager = AutocompleteConfigManager.instance
+  const settings = configManager.getSettings()
+  return settings.debounceMs || UI_UPDATE_DEBOUNCE_MS
+}
+
+const debouncedGenerateCompletion = createDebouncedFn(generateCompletion, getDebounceMs())
+const debouncedGenerateFimCompletion = createDebouncedFn(generateFimCompletion, getDebounceMs())
+```
+
+### 2. **修复 Temperature 和 Max Tokens 设置**
+
+#### FIM Handler 接口扩展
+```typescript
+export interface FimHandlerOptions {
+  apiKey: string
+  baseUrl: string
+  requestTimeoutMs?: number
+  customHeaders?: Record<string, string>
+  maxTokens?: number      // 新增
+  temperature?: number    // 新增
+}
+```
+
+#### 实际使用配置值
+```typescript
+// 非流式请求
+const body = {
+  prompt: limitedPrompt,
+  suffix: limitedSuffix,
+  stream: false,
+  max_tokens: this.options.maxTokens || 100,     // 使用配置值
+  temperature: this.options.temperature || 0.1,  // 使用配置值
+}
+
+// 流式请求
+const body = {
+  prompt: limitedPrompt,
+  suffix: limitedSuffix,
+  stream: true,
+  max_tokens: this.options.maxTokens || 100,     // 使用配置值
+  temperature: this.options.temperature || 0.1,  // 使用配置值
+}
+```
+
+#### 传递配置参数
+```typescript
+// AutocompleteProvider 中创建 FIM Handler
+fimHandler = buildFimHandler({
+  apiKey: settings.fim.apiKey,
+  baseUrl: settings.fim.baseUrl,
+  requestTimeoutMs: settings.requestTimeoutMs,
+  customHeaders: settings.customHeaders,
+  maxTokens: settings.maxTokens,        // 传递 maxTokens
+  temperature: settings.temperature,    // 传递 temperature
+})
+```
+
+## UI 改进
+
+### 新增 Debounce 时间设置字段
+```tsx
+<VSCodeTextField
+  value={localSettings.debounceMs?.toString() || ""}
+  style={{ width: "100%" }}
+  onInput={(e) =>
+    handleInputChange("debounceMs")(parseInt((e.target as HTMLInputElement)?.value) || 300)
+  }
+  placeholder="300">
+  <span style={{ fontWeight: 500 }}>Debounce Time (ms)</span>
+</VSCodeTextField>
+```
+
+### 配置管理
+```typescript
+// 加载配置
+debounceMs: config.get("debounceMs", DEFAULT_AUTOCOMPLETE_SETTINGS.debounceMs),
+
+// 保存配置
+if (settings.debounceMs !== undefined) {
+  await config.update("debounceMs", settings.debounceMs, vscode.ConfigurationTarget.Global)
+}
+```
+
+## Package.json 配置
+
+```json
+{
+  "cline.autocomplete.debounceMs": {
+    "type": "number",
+    "default": 300,
+    "minimum": 0,
+    "maximum": 5000,
+    "description": "Debounce time in milliseconds for autocomplete requests"
+  }
+}
+```
+
+## 功能效果
+
+### 1. **可调节的响应速度**
+- **低 debounce (50-100ms)**: 更快的响应，但可能增加 API 调用频率
+- **中等 debounce (300ms)**: 平衡的响应速度和 API 调用频率（默认）
+- **高 debounce (500-1000ms)**: 减少 API 调用，但响应稍慢
+
+### 2. **正确的模型参数**
+- **Temperature**: 现在会使用用户配置的值控制生成的随机性
+- **Max Tokens**: 现在会使用用户配置的值限制生成长度
+
+### 3. **更好的用户体验**
+- 用户可以根据网络环境和使用习惯调整防抖时间
+- 模型参数设置现在真正生效
+- 更精确的控制代码补全行为
+
+## 使用建议
+
+### Debounce 时间调整
+- **快速网络 + 高性能服务器**: 100-200ms
+- **一般网络环境**: 300-500ms（推荐）
+- **慢速网络 + 节省 API 调用**: 500-1000ms
+
+### Temperature 设置
+- **0.0-0.2**: 更确定性的补全（推荐用于代码）
+- **0.3-0.7**: 平衡创造性和确定性
+- **0.8-1.0**: 更有创造性但可能不太准确
+
+### Max Tokens 设置
+- **50-100**: 短补全，适合单行或简单语句
+- **100-200**: 中等补全，适合函数体或代码块
+- **200+**: 长补全，适合复杂逻辑或多行代码
+
+现在所有的 autocomplete 设置都能正确生效，用户可以根据需要精确调整补全行为！
diff --git a/AUTOCOMPLETE_TESTING_GUIDE.md b/AUTOCOMPLETE_TESTING_GUIDE.md
new file mode 100644
index ********..122f3e81
--- /dev/null
+++ b/AUTOCOMPLETE_TESTING_GUIDE.md
@@ -0,0 +1,144 @@
+# QAX Autocomplete Testing Guide
+
+This guide provides step-by-step instructions for testing the QAX Autocomplete integration in Cline.
+
+## Prerequisites
+
+1. Build the extension: `npm run compile`
+2. Install dependencies: `npm install`
+3. Have a valid API key for OpenRouter or compatible service
+
+## Manual Testing Steps
+
+### 1. Extension Loading Test
+
+1. Open VS Code with the Cline extension
+2. Check that the extension loads without errors
+3. Verify that no console errors related to autocomplete appear
+
+### 2. Configuration UI Test
+
+1. Open Cline settings (click the settings gear icon)
+2. Navigate to the "Autocomplete" tab
+3. Verify the following fields are present:
+   - Enable QAX Autocomplete (checkbox)
+   - API Key (password field)
+   - API Base URL (text field, default: https://api.openrouter.ai/api/v1)
+   - Model ID (text field, default: google/gemini-2.5-flash-preview-05-20)
+   - Max Tokens (number field, default: 1000)
+   - Temperature (number field, default: 0.1)
+   - Request Timeout (number field, default: 30000)
+   - Use Prompt Cache (checkbox)
+
+### 3. Configuration Persistence Test
+
+1. Configure autocomplete settings:
+   - Enable QAX Autocomplete: ✓
+   - API Key: [Your API Key]
+   - Leave other settings as default
+2. Click "Save Settings"
+3. Close and reopen VS Code
+4. Check that settings are preserved
+
+### 4. Status Bar Test
+
+1. With autocomplete disabled:
+   - Look for "$(circle-slash) QAX Complete" in status bar
+   - Tooltip should show "QAX Code Autocomplete (disabled)"
+
+2. With autocomplete enabled but no API key:
+   - Look for "$(warning) QAX Complete" in status bar
+   - Tooltip should show "A valid API key must be set to use autocomplete"
+
+3. With autocomplete enabled and API key set:
+   - Look for "$(sparkle) QAX Complete" in status bar
+   - Tooltip should show autocomplete information
+
+### 5. Command Test
+
+1. Open Command Palette (Ctrl+Shift+P / Cmd+Shift+P)
+2. Search for "QAX" commands
+3. Verify these commands exist:
+   - "Toggle QAX Autocomplete"
+   - "Track Accepted Suggestion"
+
+### 6. Autocomplete Functionality Test
+
+1. Enable autocomplete and set a valid API key
+2. Open a code file (JavaScript, TypeScript, Python, etc.)
+3. Start typing code
+4. Verify that autocomplete suggestions appear (if API key is valid)
+5. Test accepting suggestions
+
+### 7. Error Handling Test
+
+1. Set an invalid API key
+2. Try to use autocomplete
+3. Verify graceful error handling
+
+### 8. Configuration Validation Test
+
+1. Try to set invalid values:
+   - Max Tokens: 0 or > 10000
+   - Temperature: < 0 or > 2
+   - Request Timeout: < 1000 or > 300000
+2. Verify validation errors appear
+
+## Expected Behavior
+
+### Status Bar States
+
+- **Disabled**: "$(circle-slash) QAX Complete"
+- **No API Key**: "$(warning) QAX Complete"
+- **Active**: "$(sparkle) QAX Complete ($0.00)"
+
+### Configuration Persistence
+
+- Settings should persist across VS Code restarts
+- API key should be stored securely in VS Code secrets
+- Other settings should be stored in VS Code configuration
+
+### Error Handling
+
+- Invalid configurations should show validation errors
+- Network errors should be handled gracefully
+- No crashes should occur from autocomplete functionality
+
+## Troubleshooting
+
+### Common Issues
+
+1. **Autocomplete not appearing**:
+   - Check that autocomplete is enabled
+   - Verify API key is set and valid
+   - Check network connectivity
+   - Look for errors in VS Code Developer Console
+
+2. **Settings not saving**:
+   - Check for validation errors
+   - Verify VS Code has write permissions
+   - Try restarting VS Code
+
+3. **Status bar not updating**:
+   - Check that the extension is active
+   - Try toggling autocomplete on/off
+   - Restart VS Code
+
+### Debug Information
+
+- Check VS Code Developer Console for errors
+- Look for autocomplete-related log messages
+- Verify network requests in browser dev tools (if applicable)
+
+## Test Checklist
+
+- [ ] Extension loads without errors
+- [ ] Autocomplete tab appears in settings
+- [ ] All configuration fields are present
+- [ ] Settings save and persist
+- [ ] Status bar shows correct states
+- [ ] Commands are registered
+- [ ] Autocomplete functionality works (with valid API key)
+- [ ] Error handling works correctly
+- [ ] Configuration validation works
+- [ ] No memory leaks or performance issues
diff --git a/AUTOCOMPLETE_UI_SIMPLIFICATION.md b/AUTOCOMPLETE_UI_SIMPLIFICATION.md
new file mode 100644
index ********..c567453d
--- /dev/null
+++ b/AUTOCOMPLETE_UI_SIMPLIFICATION.md
@@ -0,0 +1,157 @@
+# Autocomplete UI 简化
+
+## 修改内容
+
+### 去掉条件显示，直接显示所有配置字段
+
+#### 修改前
+```tsx
+{localSettings.provider !== "fim" && (
+  <>
+    <ApiKeyField value={localSettings.apiKey || ""} ... />
+    <BaseUrlField value={localSettings.apiBaseUrl || ""} ... />
+  </>
+)}
+
+{localSettings.provider === "fim" && (
+  <>
+    <ApiKeyField value={localSettings.fim?.apiKey || ""} ... />
+    <BaseUrlField value={localSettings.fim?.baseUrl || ""} ... />
+  </>
+)}
+```
+
+#### 修改后
+```tsx
+<ApiKeyField
+  value={localSettings.apiKey || ""}
+  onChange={handleInputChange("apiKey")}
+  providerName="OpenAI Compatible API"
+  placeholder="Enter your API key..."
+/>
+
+<BaseUrlField
+  value={localSettings.apiBaseUrl || ""}
+  onChange={handleInputChange("apiBaseUrl")}
+  placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
+  label="API Base URL"
+/>
+
+<ApiKeyField
+  value={localSettings.fim?.apiKey || ""}
+  onChange={handleInputChange("fim.apiKey")}
+  providerName="FIM API"
+  placeholder="Enter your FIM API key..."
+/>
+
+<BaseUrlField
+  value={localSettings.fim?.baseUrl || ""}
+  onChange={handleInputChange("fim.baseUrl")}
+  placeholder="https://your-fim-api.com/v1"
+  label="FIM API Base URL"
+/>
+```
+
+## 改进效果
+
+### 1. **简化用户体验**
+- 不需要切换 Provider 来查看不同的配置选项
+- 所有配置字段始终可见，用户可以一次性配置所有选项
+- 减少了界面的动态变化，提供更稳定的用户体验
+
+### 2. **提高配置效率**
+- 用户可以同时配置 OpenAI 和 FIM 的设置
+- 不需要在不同 Provider 之间切换来填写配置
+- 便于比较和管理不同 API 的配置
+
+### 3. **更清晰的配置结构**
+```
+Provider 选择: [OpenAI Compatible ▼] [FIM (Fill in the Middle)]
+
+OpenAI Compatible API:
+├── API Key: [输入框]
+└── API Base URL: [输入框]
+
+FIM API:
+├── API Key: [输入框]
+└── API Base URL: [输入框]
+
+通用设置:
+├── Model ID: [输入框]
+├── Max Tokens: [输入框]
+├── Temperature: [输入框]
+├── Request Timeout: [输入框]
+├── Debounce Time: [输入框]
+└── Use Prompt Cache: [复选框]
+```
+
+### 4. **保持功能完整性**
+- Provider 选择仍然有效，决定实际使用哪个 API
+- 所有配置都会被保存，无论当前选择哪个 Provider
+- 配置验证逻辑保持不变
+
+## 使用场景
+
+### 1. **多 API 环境**
+用户可能需要在不同情况下使用不同的 API：
+- 开发环境使用本地 FIM API
+- 生产环境使用 OpenAI Compatible API
+- 备用 API 配置以防主要 API 不可用
+
+### 2. **快速切换**
+- 预先配置好两种 API 的设置
+- 通过 Provider 下拉框快速切换
+- 无需重新输入配置信息
+
+### 3. **配置管理**
+- 一次性查看所有配置选项
+- 便于检查配置的完整性
+- 减少配置错误的可能性
+
+## 技术实现
+
+### 配置字段映射
+```typescript
+// OpenAI Compatible
+apiKey: string
+apiBaseUrl: string
+
+// FIM
+fim.apiKey: string
+fim.baseUrl: string
+
+// 通用设置
+provider: "openai" | "fim"
+modelId: string
+maxTokens: number
+temperature: number
+// ...
+```
+
+### 验证逻辑
+```typescript
+// 根据选择的 provider 验证相应的配置
+if (settings.provider === "fim") {
+  // 验证 FIM 配置
+  if (!settings.fim?.apiKey) errors.push("FIM API key required")
+  if (!settings.fim?.baseUrl) errors.push("FIM base URL required")
+} else {
+  // 验证 OpenAI 配置
+  if (!settings.apiKey) errors.push("API key required")
+}
+```
+
+## 用户体验改进
+
+### 之前的问题
+- 需要切换 Provider 才能看到相应的配置选项
+- 容易忘记配置某个 Provider 的设置
+- 界面动态变化可能造成困惑
+
+### 现在的优势
+- 所有配置选项一目了然
+- 可以预先配置多个 API 选项
+- 界面稳定，用户体验更好
+- 配置更加直观和高效
+
+这个修改让 autocomplete 配置页面更加用户友好，提供了更好的配置管理体验！
diff --git a/BASE_URL_FIELD_SIMPLIFICATION.md b/BASE_URL_FIELD_SIMPLIFICATION.md
new file mode 100644
index ********..b80a5b24
--- /dev/null
+++ b/BASE_URL_FIELD_SIMPLIFICATION.md
@@ -0,0 +1,173 @@
+# Base URL 字段简化
+
+## 修改内容
+
+### 去掉 Checkbox 开关，直接显示输入框
+
+#### 修改前的 BaseUrlField 组件
+```tsx
+// 包含 checkbox 开关的复杂组件
+export const BaseUrlField = ({ value, onChange, label, placeholder }) => {
+  const [isEnabled, setIsEnabled] = useState(!!value)
+  
+  const handleToggle = (e) => {
+    const checked = e.target.checked
+    setIsEnabled(checked)
+    if (!checked) {
+      onChange("")
+    }
+  }
+
+  return (
+    <div>
+      <VSCodeCheckbox checked={isEnabled} onChange={handleToggle}>
+        {label}
+      </VSCodeCheckbox>
+      
+      {isEnabled && (
+        <VSCodeTextField
+          value={value || ""}
+          style={{ width: "100%", marginTop: 3 }}
+          type="url"
+          onInput={(e) => onChange(e.target.value)}
+          placeholder={placeholder}
+        />
+      )}
+    </div>
+  )
+}
+```
+
+#### 修改后的 BaseUrlField 组件
+```tsx
+// 简化的直接输入框组件
+export const BaseUrlField = ({ value, onChange, label, placeholder }) => {
+  return (
+    <VSCodeTextField
+      value={value || ""}
+      style={{ width: "100%" }}
+      type="url"
+      onInput={(e) => onChange(e.target.value)}
+      placeholder={placeholder}>
+      <span style={{ fontWeight: 500 }}>{label}</span>
+    </VSCodeTextField>
+  )
+}
+```
+
+## 改进效果
+
+### 1. **简化用户操作**
+- **之前**: 用户需要先点击 checkbox 开启，然后才能输入 URL
+- **现在**: 用户可以直接输入 URL，无需额外操作
+
+### 2. **减少界面复杂性**
+- **之前**: 每个 Base URL 字段都有一个 checkbox + 条件显示的输入框
+- **现在**: 每个 Base URL 字段就是一个简单的输入框
+
+### 3. **更直观的用户体验**
+- **之前**: 用户可能不知道需要先勾选 checkbox
+- **现在**: 用户一眼就能看到可以输入 URL 的地方
+
+## UI 变化对比
+
+### 修改前
+```
+┌─ API Base URL ─────────────────────────┐
+│ ☐ Use custom base URL                  │
+│                                        │
+│ (输入框只有在勾选后才显示)              │
+└────────────────────────────────────────┘
+```
+
+### 修改后
+```
+┌─ API Base URL ─────────────────────────┐
+│ API Base URL                           │
+│ [https://api.openrouter.ai/api/v1...] │
+└────────────────────────────────────────┘
+```
+
+## 在 Autocomplete 配置中的应用
+
+### OpenAI Compatible Provider
+```tsx
+<BaseUrlField
+  value={localSettings.apiBaseUrl || ""}
+  onChange={handleInputChange("apiBaseUrl")}
+  placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
+  label="API Base URL"
+/>
+```
+
+### FIM Provider
+```tsx
+<BaseUrlField
+  value={localSettings.fim?.baseUrl || ""}
+  onChange={handleInputChange("fim.baseUrl")}
+  placeholder="https://your-fim-api.com/v1"
+  label="FIM Base URL"
+/>
+```
+
+## 技术改进
+
+### 1. **组件简化**
+- 移除了 `useState` 和 `useEffect` 的状态管理
+- 移除了 `handleToggle` 函数
+- 移除了条件渲染逻辑
+
+### 2. **代码减少**
+- 从 58 行代码减少到 34 行代码
+- 移除了不必要的复杂性
+- 更容易维护和理解
+
+### 3. **性能提升**
+- 减少了状态管理的开销
+- 减少了重新渲染的次数
+- 更轻量的组件实现
+
+## 用户体验改进
+
+### 1. **降低学习成本**
+- 用户不需要理解 checkbox 的作用
+- 直接看到输入框就知道可以输入
+- 减少了操作步骤
+
+### 2. **提高配置效率**
+- 无需额外的点击操作
+- 可以直接开始输入 URL
+- 减少了配置时间
+
+### 3. **更好的可访问性**
+- 减少了界面元素的数量
+- 更清晰的标签和占位符
+- 更直观的操作流程
+
+## 兼容性
+
+### 1. **API 兼容性**
+- 组件的 props 接口保持不变
+- 外部调用代码无需修改
+- 向后兼容现有实现
+
+### 2. **功能兼容性**
+- 仍然支持空值处理
+- 仍然支持 URL 验证
+- 仍然支持占位符显示
+
+### 3. **样式兼容性**
+- 保持相同的宽度设置
+- 保持相同的字体样式
+- 保持相同的间距布局
+
+## 使用场景
+
+这种简化特别适合以下场景：
+
+1. **必填配置**: 当 Base URL 是必需的配置项时
+2. **简化界面**: 当希望减少界面复杂性时
+3. **快速配置**: 当用户需要快速设置多个 URL 时
+4. **移动端**: 当在小屏幕设备上使用时
+
+现在 Base URL 配置变得更加简单直观，用户可以直接输入而无需额外的开关操作！
diff --git a/FIM_CONNECTION_STABILITY_FIXES.md b/FIM_CONNECTION_STABILITY_FIXES.md
new file mode 100644
index ********..d769449f
--- /dev/null
+++ b/FIM_CONNECTION_STABILITY_FIXES.md
@@ -0,0 +1,151 @@
+# FIM 连接稳定性修复
+
+## 问题分析
+
+错误 "terminated" 通常表示流式连接被意外中断，可能的原因：
+
+1. **服务器端主动关闭连接**
+2. **网络连接不稳定**
+3. **代理或防火墙干扰**
+4. **服务器处理超时**
+5. **流式响应格式问题**
+
+## 修复措施
+
+### 1. **重试机制**
+```typescript
+async *completePromptStream(prompt: string, suffix?: string): AsyncGenerator<string> {
+  const maxRetries = 2
+  
+  for (let attempt = 0; attempt <= maxRetries; attempt++) {
+    try {
+      yield* this.completePromptStreamInternal(prompt, suffix)
+      return // 成功，退出重试循环
+    } catch (error) {
+      // 指数退避重试：1s, 2s, 4s...
+      if (attempt < maxRetries) {
+        const delay = Math.pow(2, attempt) * 1000
+        await new Promise(resolve => setTimeout(resolve, delay))
+      }
+    }
+  }
+}
+```
+
+### 2. **改进的流式处理**
+```typescript
+// 添加缓冲区处理不完整的行
+let buffer = ""
+const lines = buffer.split('\n')
+buffer = lines.pop() || "" // 保留最后一个不完整的行
+```
+
+### 3. **更好的请求头**
+```typescript
+const headers = {
+  "Content-Type": "application/json",
+  "Authorization": `Bearer ${apiKey}`,
+  "Accept": "text/event-stream",        // 明确接受 SSE
+  "Cache-Control": "no-cache",          // 禁用缓存
+  "Connection": "keep-alive",           // 保持连接
+}
+```
+
+### 4. **详细的错误处理**
+```typescript
+if (error.message.includes('terminated') || error.message.includes('aborted')) {
+  throw new Error(`FIM API streaming connection terminated:
+URL: ${url}
+Error: ${error.message}
+Possible causes: Server closed connection, network instability, or proxy interference
+Suggestion: Check server logs, network connectivity, or try reducing request frequency`)
+}
+```
+
+### 5. **连接健康检查**
+```typescript
+async testConnection(): Promise<boolean> {
+  try {
+    const response = await fetch(url, {
+      method: "POST",
+      headers,
+      body: JSON.stringify({
+        prompt: "test",
+        suffix: "",
+        stream: false,
+        max_tokens: 1,
+        temperature: 0.1,
+      }),
+      signal: AbortSignal.timeout(5000), // 5秒超时
+    })
+    
+    return response.ok || response.status === 400
+  } catch (error) {
+    return false
+  }
+}
+```
+
+### 6. **详细的调试日志**
+```typescript
+console.log('FIM API streaming attempt 1/3')
+console.log('FIM API received chunk:', chunk)
+console.log('FIM API stream completed normally')
+console.warn('FIM API streaming attempt 1 failed:', error)
+```
+
+## 调试建议
+
+### 检查服务器日志
+1. 查看 `kubemlsvc.qianxin-inc.cn` 的服务器日志
+2. 确认是否有连接超时或资源限制
+3. 检查是否有请求频率限制
+
+### 网络诊断
+```bash
+# 测试连接
+curl -v http://kubemlsvc.qianxin-inc.cn/svc-d70rsdpypzxs/v1/completions
+
+# 检查代理设置
+echo $HTTP_PROXY
+echo $HTTPS_PROXY
+```
+
+### 配置调整建议
+
+1. **增加超时时间**：
+```typescript
+requestTimeoutMs: 60000 // 增加到60秒
+```
+
+2. **减少并发请求**：
+确保同时只有一个 FIM 请求在进行
+
+3. **检查防火墙设置**：
+确保企业防火墙不会中断长连接
+
+## 预期改进
+
+### 重试机制
+- 自动重试最多3次
+- 指数退避延迟（1s, 2s, 4s）
+- 不重试超时错误
+
+### 连接稳定性
+- 正确的 SSE 请求头
+- 缓冲区处理不完整数据
+- 详细的连接状态日志
+
+### 错误诊断
+- 明确的错误分类
+- 详细的调试信息
+- 具体的解决建议
+
+## 使用建议
+
+1. **监控日志**：观察重试次数和成功率
+2. **网络检查**：确认到服务器的连接稳定性
+3. **配置优化**：根据网络环境调整超时时间
+4. **错误报告**：收集详细的错误信息用于进一步诊断
+
+现在 FIM API 应该能更好地处理连接中断问题，并提供详细的诊断信息！
diff --git a/FIM_CONTEXT_LIMITING.md b/FIM_CONTEXT_LIMITING.md
new file mode 100644
index ********..2dd60c93
--- /dev/null
+++ b/FIM_CONTEXT_LIMITING.md
@@ -0,0 +1,135 @@
+# FIM 上下文限制实现
+
+## 问题背景
+
+为了避免 FIM API 请求过大导致的性能问题和连接中断，需要限制发送的上下文大小。
+
+## 实现的限制策略
+
+### 1. **行数限制（AutocompleteProvider 层面）**
+
+```typescript
+// 限制总行数为 100 行
+const maxLines = 100
+const currentLine = position.line
+
+// 70% 用于光标前的上下文，30% 用于光标后的上下文
+const linesBeforeLimit = Math.min(currentLine, Math.floor(maxLines * 0.7))
+const linesAfterLimit = Math.min(document.lineCount - currentLine - 1, maxLines - linesBeforeLimit)
+
+// 计算实际的起始和结束行
+const startLine = Math.max(0, currentLine - linesBeforeLimit)
+const endLine = Math.min(document.lineCount, currentLine + linesAfterLimit + 1)
+```
+
+**特点**：
+- 总共最多 100 行代码
+- 光标前最多 70 行（保留更多历史上下文）
+- 光标后最多 30 行（适量的后续上下文）
+- 动态调整：如果前面行数不足，会给后面分配更多行数
+
+### 2. **字符数限制（FIM Handler 层面）**
+
+```typescript
+// 限制总字符数为 8000 字符
+const maxContextLength = 8000
+const limitedPrompt = this.limitContextSize(prompt, maxContextLength * 0.7)  // 5600 字符
+const limitedSuffix = this.limitContextSize(suffix, maxContextLength * 0.3)  // 2400 字符
+```
+
+**特点**：
+- 总共最多 8000 字符
+- Prompt（光标前）最多 5600 字符
+- Suffix（光标后）最多 2400 字符
+- 智能截取：保留最相关的上下文
+
+### 3. **智能截取算法**
+
+```typescript
+private limitContextSize(text: string, maxLength: number): string {
+  if (text.length <= maxLength) {
+    return text
+  }
+  
+  const lines = text.split('\n')
+  
+  // 对于 prompt（光标前），保留末尾（最近的上下文）
+  if (!text.startsWith(' ') && !text.startsWith('\t')) {
+    // 从末尾开始保留行
+    for (let i = lines.length - 1; i >= 0; i--) {
+      // 逐行添加直到达到长度限制
+    }
+  } else {
+    // 对于 suffix（光标后），保留开头
+    for (const line of lines) {
+      // 从开头逐行添加直到达到长度限制
+    }
+  }
+}
+```
+
+## 限制效果
+
+### 请求大小控制
+- **行数限制**: 最多 100 行代码
+- **字符限制**: 最多 8000 字符
+- **双重保护**: 两层限制确保请求不会过大
+
+### 上下文质量
+- **智能分配**: 70% 给历史上下文，30% 给后续上下文
+- **相关性优先**: 保留最接近光标的代码
+- **完整性保护**: 按行截取，避免截断代码结构
+
+### 性能优化
+- **减少网络传输**: 更小的请求体积
+- **降低服务器负载**: 减少处理的文本量
+- **提高响应速度**: 更快的处理和传输
+
+## 调试信息
+
+### AutocompleteProvider 日志
+```
+🚀📏 FIM context: 45 lines before, 30 lines after (total: 75 lines)
+```
+
+### FIM Handler 日志
+```
+🚀📏 FIM context sizes - prompt: 3200 chars, suffix: 1800 chars
+🚀📏 FIM streaming context sizes - prompt: 3200 chars, suffix: 1800 chars
+```
+
+## 配置建议
+
+### 可调整的参数
+
+1. **最大行数** (AutocompleteProvider):
+```typescript
+const maxLines = 100  // 可根据需要调整
+```
+
+2. **最大字符数** (FIM Handler):
+```typescript
+const maxContextLength = 8000  // 可根据 API 限制调整
+```
+
+3. **分配比例**:
+```typescript
+const beforeRatio = 0.7  // 70% 给光标前
+const afterRatio = 0.3   // 30% 给光标后
+```
+
+### 根据文件类型优化
+
+可以考虑根据不同文件类型调整限制：
+- **大型文件**: 更严格的限制
+- **配置文件**: 可以适当放宽
+- **脚本文件**: 重点保留函数定义
+
+## 预期效果
+
+1. **连接稳定性提升**: 减少因请求过大导致的连接中断
+2. **响应速度提升**: 更小的上下文处理更快
+3. **服务器负载降低**: 减少计算资源消耗
+4. **用户体验改善**: 更稳定的代码补全功能
+
+现在 FIM API 请求将被限制在合理的大小范围内，应该能显著改善连接稳定性！
diff --git a/FIM_ERROR_HANDLING_IMPROVEMENTS.md b/FIM_ERROR_HANDLING_IMPROVEMENTS.md
new file mode 100644
index ********..3296d639
--- /dev/null
+++ b/FIM_ERROR_HANDLING_IMPROVEMENTS.md
@@ -0,0 +1,107 @@
+# FIM API 错误处理改进
+
+## 改进概述
+
+已经将 FIM (Fill in the Middle) API 处理器的错误信息进行了详细化改进，现在包含：
+- **URL**: 请求的完整 URL
+- **HTTP 状态码**: 详细的状态码和状态文本
+- **响应头**: 完整的响应头信息
+- **错误消息**: 服务器返回的详细错误信息
+
+## 改进的错误类型
+
+### 1. HTTP 错误响应（非流式）
+```
+FIM API request failed:
+URL: https://your-fim-api.com/v1/completions
+HTTP Status: 401 Unauthorized
+Response Headers: {"content-type":"application/json","www-authenticate":"Bearer"}
+Error Message: {"error":"Invalid API key"}
+```
+
+### 2. HTTP 错误响应（流式）
+```
+FIM API streaming request failed:
+URL: https://your-fim-api.com/v1/completions
+HTTP Status: 429 Too Many Requests
+Response Headers: {"retry-after":"60","content-type":"application/json"}
+Error Message: {"error":"Rate limit exceeded"}
+```
+
+### 3. 请求超时错误（非流式）
+```
+FIM API request timed out:
+URL: https://your-fim-api.com/v1/completions
+Timeout: 30000ms
+```
+
+### 4. 请求超时错误（流式）
+```
+FIM API streaming request timed out:
+URL: https://your-fim-api.com/v1/completions
+Timeout: 30000ms
+```
+
+### 5. 网络错误（非流式）
+```
+FIM API request failed:
+URL: https://your-fim-api.com/v1/completions
+Error: Failed to fetch
+```
+
+### 6. 网络错误（流式）
+```
+FIM API streaming request failed:
+URL: https://your-fim-api.com/v1/completions
+Error: Network error occurred
+```
+
+### 7. 未知错误
+```
+FIM API request failed with unknown error:
+URL: https://your-fim-api.com/v1/completions
+```
+
+## 技术实现
+
+### 响应头处理
+由于浏览器环境中 `Headers.entries()` 方法的兼容性问题，使用了 `forEach` 方法来安全地提取响应头：
+
+```typescript
+const responseHeaders: Record<string, string> = {}
+response.headers.forEach((value, key) => {
+    responseHeaders[key] = value
+})
+```
+
+### 错误信息结构化
+所有错误信息都采用统一的多行格式，便于调试：
+- 第一行：错误类型描述
+- URL：完整的请求 URL
+- HTTP Status：状态码和状态文本（如适用）
+- Response Headers：JSON 格式的响应头（如适用）
+- Error Message/Timeout：具体的错误信息或超时时间
+
+## 调试优势
+
+1. **快速定位问题**: 通过 URL 可以立即知道请求的目标
+2. **状态码分析**: HTTP 状态码帮助快速判断错误类型
+3. **响应头信息**: 提供额外的调试信息（如 rate limiting、认证要求等）
+4. **详细错误消息**: 服务器返回的具体错误信息
+5. **超时信息**: 明确的超时时间设置
+
+## 使用场景
+
+这些详细的错误信息特别有助于：
+- **API 集成调试**: 快速识别配置问题
+- **网络问题排查**: 区分网络错误和服务器错误
+- **认证问题**: 通过状态码和响应头快速识别认证问题
+- **限流处理**: 识别 rate limiting 并获取重试信息
+- **生产环境监控**: 提供足够的信息进行问题诊断
+
+## 编译状态
+
+✅ **TypeScript 编译通过**
+✅ **ESLint 检查通过**
+✅ **所有 linting 警告已修复**
+✅ **错误处理逻辑完整**
diff --git a/FIM_STREAMING_FIXES.md b/FIM_STREAMING_FIXES.md
new file mode 100644
index ********..d54774f6
--- /dev/null
+++ b/FIM_STREAMING_FIXES.md
@@ -0,0 +1,142 @@
+# FIM 流式处理修复
+
+## 修复的问题
+
+### 1. **Stream 参数设置错误**
+- ✅ **修复前**: `completePrompt` 方法错误地设置了 `stream: true`
+- ✅ **修复后**: `completePrompt` 使用 `stream: false`，`completePromptStream` 使用 `stream: true`
+
+### 2. **JSON 解析逻辑改进**
+根据您提供的响应格式：
+```json
+{
+  "id": "8d75e06270a342a68df1975195c9f690",
+  "object": "text_completion", 
+  "created": 1750904818,
+  "model": "._fp16_vllm",
+  "choices": [
+    {
+      "text": "[j",
+      "index": 0,
+      "finish_reason": null,
+      "logprobs": {
+        "tokens": null,
+        "token_logprobs": null,
+        "top_logprobs": null,
+        "text_offset": null
+      }
+    }
+  ],
+  "usage": {
+    "prompt_tokens": 13,
+    "completion_tokens": 1,
+    "total_tokens": 14
+  }
+}
+```
+
+- ✅ **改进解析逻辑**: 正确提取 `choices[0].text` 字段
+- ✅ **添加调试日志**: 帮助诊断 JSON 解析问题
+- ✅ **错误处理**: 改进 JSON 解析失败的错误处理
+
+### 3. **流式处理集成**
+- ✅ **扩展接口**: 在 `SingleCompletionHandler` 中添加可选的 `completePromptStream` 方法
+- ✅ **智能选择**: AutocompleteProvider 优先使用流式处理，如果不可用则回退到非流式
+- ✅ **实时体验**: 流式处理提供更好的实时代码补全体验
+
+## 技术实现
+
+### 方法分离
+```typescript
+// 非流式方法
+async completePrompt(prompt: string, suffix?: string): Promise<string> {
+  const body = {
+    prompt: prompt,
+    suffix: suffix || "",
+    stream: false,  // 非流式
+    max_tokens: 100,
+    temperature: 0.1,
+  }
+  // ... 处理逻辑
+}
+
+// 流式方法  
+async *completePromptStream(prompt: string, suffix?: string): AsyncGenerator<string> {
+  const body = {
+    prompt: prompt,
+    suffix: suffix || "",
+    stream: true,   // 流式
+    max_tokens: 100,
+    temperature: 0.1,
+  }
+  // ... 流式处理逻辑
+}
+```
+
+### 响应解析
+```typescript
+// 流式响应解析
+for (const line of lines) {
+  if (line.startsWith('data: ')) {
+    const data = line.slice(6)
+    if (data === '[DONE]') continue
+    
+    try {
+      const parsed = JSON.parse(data)
+      if (parsed.choices && parsed.choices.length > 0) {
+        const text = parsed.choices[0].text || ""
+        if (text) {
+          yield text
+        }
+      }
+    } catch (e) {
+      console.warn('JSON parse error:', e, 'Data:', data)
+    }
+  }
+}
+```
+
+### AutocompleteProvider 集成
+```typescript
+// 智能选择流式或非流式
+if (fimHandler.completePromptStream) {
+  // 使用流式处理
+  for await (const chunk of fimHandler.completePromptStream(textBeforeCursor, textAfterCursor)) {
+    completion += chunk
+  }
+} else {
+  // 回退到非流式
+  completion = await fimHandler.completePrompt(textBeforeCursor, textAfterCursor)
+}
+```
+
+## 调试功能
+
+### 添加的调试日志
+1. **响应解析**: `console.log('FIM API parsed response:', parsed)`
+2. **文本提取**: `console.log('FIM API yielding text:', text)`
+3. **JSON 错误**: `console.warn('FIM API JSON parse error:', e, 'Data:', data)`
+4. **非流式响应**: `console.log('FIM API non-streaming response:', data)`
+
+## 预期行为
+
+### 流式处理流程
+1. 发送 POST 请求，`stream: true`
+2. 接收 SSE 格式的响应流
+3. 解析每行 `data: {...}` 格式的 JSON
+4. 提取 `choices[0].text` 字段
+5. 逐步构建完整的补全文本
+
+### 错误处理
+- 详细的错误信息包含 URL、HTTP 状态码、响应头
+- JSON 解析错误会被记录但不会中断流式处理
+- 网络错误和超时都有明确的错误消息
+
+## 测试建议
+
+1. **检查网络请求**: 确认发送的 `stream` 参数正确
+2. **查看调试日志**: 检查 JSON 解析是否成功
+3. **验证响应格式**: 确认服务器返回的格式符合预期
+4. **测试错误场景**: 验证错误处理是否正常工作
+
+现在 FIM API 应该能正确处理您提供的响应格式，并且支持真正的流式处理！
diff --git a/README.md b/README.md
index 479c984f..59aea155 100644
--- a/README.md
+++ b/README.md
@@ -125,6 +125,26 @@ Thanks to the [Model Context Protocol](https://github.com/modelcontextprotocol),
 
 <img width="2000" height="0" src="https://github.com/user-attachments/assets/ee14e6f7-20b8-4391-9091-8e8e25561929"><br>
 
+<img align="left" width="360" src="https://github.com/user-attachments/assets/7fdf41e6-281a-4b4b-ac19-020b838b6970">
+
+### QAX Autocomplete
+
+Cline now includes **QAX Autocomplete**, an intelligent code completion feature that provides AI-powered suggestions as you type using OpenAI-compatible APIs. Configure it in the Autocomplete settings tab to:
+
+- Get real-time code suggestions powered by your choice of AI models
+- Support for multiple programming languages and any OpenAI-compatible API
+- Works with OpenRouter, OpenAI, local models (Ollama, LM Studio), and more
+- Cost tracking and usage monitoring
+- Configurable models and parameters for optimal performance
+
+Simply enable QAX Autocomplete, configure your preferred API endpoint and key, and start coding with intelligent AI assistance right in your editor.
+
+[Learn more about QAX Autocomplete →](docs/QAX_AUTOCOMPLETE.md)
+
+<!-- Transparent pixel to create line break after floating image -->
+
+<img width="2000" height="0" src="https://github.com/user-attachments/assets/ee14e6f7-20b8-4391-9091-8e8e25561929"><br>
+
 <img align="right" width="350" src="https://github.com/user-attachments/assets/140c8606-d3bf-41b9-9a1f-4dbf0d4c90cb">
 
 ### Checkpoints: Compare and Restore
diff --git a/docs/QAX_AUTOCOMPLETE.md b/docs/QAX_AUTOCOMPLETE.md
new file mode 100644
index ********..a7e30f55
--- /dev/null
+++ b/docs/QAX_AUTOCOMPLETE.md
@@ -0,0 +1,182 @@
+# QAX Autocomplete
+
+QAX Autocomplete is an intelligent code completion feature integrated into Cline that provides AI-powered code suggestions as you type.
+
+## Features
+
+- **Real-time code completion**: Get intelligent code suggestions as you type
+- **Multiple language support**: Works with JavaScript, TypeScript, Python, Java, and more
+- **Configurable AI models**: Choose from various AI models for code generation
+- **Cost tracking**: Monitor API usage and costs in real-time
+- **Context-aware suggestions**: Leverages your current code context for better suggestions
+
+## Setup
+
+### 1. Enable QAX Autocomplete
+
+1. Open Cline settings by clicking the settings gear icon (⚙️) in the Cline sidebar
+2. Navigate to the **Autocomplete** tab
+3. Check the **Enable QAX Autocomplete** checkbox
+
+### 2. Configure API Settings
+
+QAX Autocomplete uses OpenAI-compatible APIs, giving you flexibility to choose from various providers:
+
+#### Option 1: OpenRouter (Recommended)
+1. Sign up at [OpenRouter](https://openrouter.ai/)
+2. Get your API key from the dashboard
+3. In Cline's Autocomplete settings:
+   - **API Base URL**: `https://api.openrouter.ai/api/v1` (default)
+   - **API Key**: Your OpenRouter API key
+   - **Model ID**: `google/gemini-2.5-flash-preview-05-20` (default, or choose another)
+
+#### Option 2: OpenAI Direct
+1. Get your API key from [OpenAI](https://platform.openai.com/)
+2. In Cline's Autocomplete settings:
+   - **API Base URL**: `https://api.openai.com/v1`
+   - **API Key**: Your OpenAI API key
+   - **Model ID**: `gpt-4o-mini` or `gpt-4o`
+
+#### Option 3: Local Models
+- **Ollama**: Set API Base URL to `http://localhost:11434/v1`
+- **LM Studio**: Set API Base URL to `http://localhost:1234/v1`
+- **Other local servers**: Use the appropriate local endpoint
+
+#### Option 4: Other Compatible Services
+QAX Autocomplete works with any OpenAI-compatible API:
+- Azure OpenAI
+- Anthropic (via proxy)
+- Together AI
+- Fireworks AI
+- Any other OpenAI-compatible endpoint
+
+### 3. Configure Advanced Settings (Optional)
+
+- **Max Tokens**: Maximum number of tokens for each completion (default: 1000)
+- **Temperature**: Controls randomness of suggestions (0.0-2.0, default: 0.1)
+- **Request Timeout**: API request timeout in milliseconds (default: 30000)
+- **Use Prompt Cache**: Enable prompt caching for better performance (if supported)
+
+## Usage
+
+### Getting Suggestions
+
+1. Open any code file in VS Code
+2. Start typing code
+3. QAX Autocomplete will automatically show suggestions as you type
+4. Press `Tab` or `Enter` to accept a suggestion
+5. Press `Esc` to dismiss suggestions
+
+### Status Bar
+
+The QAX Autocomplete status is shown in the VS Code status bar:
+
+- **🌟 QAX Complete ($0.00)**: Active and showing current session cost
+- **⚠️ QAX Complete**: Warning (usually missing API key)
+- **🚫 QAX Complete**: Disabled
+
+Click the status bar item to toggle autocomplete on/off.
+
+### Commands
+
+Access these commands via the Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`):
+
+- **Toggle QAX Autocomplete**: Enable/disable autocomplete
+- **Track Accepted Suggestion**: Internal command for usage tracking
+
+## Configuration Reference
+
+### Basic Settings
+
+| Setting | Description | Default |
+|---------|-------------|---------|
+| **Enable QAX Autocomplete** | Master on/off switch | `false` |
+| **API Key** | Your API key (stored securely) | - |
+| **API Base URL** | Base URL for the API service | `https://api.openrouter.ai/api/v1` |
+| **Model ID** | AI model to use for completions | `google/gemini-2.5-flash-preview-05-20` |
+
+### Advanced Settings
+
+| Setting | Description | Default | Range |
+|---------|-------------|---------|-------|
+| **Max Tokens** | Maximum tokens per completion | `1000` | 1-10000 |
+| **Temperature** | Randomness of suggestions | `0.1` | 0.0-2.0 |
+| **Request Timeout** | API timeout in milliseconds | `30000` | 1000-300000 |
+| **Use Prompt Cache** | Enable prompt caching | `false` | - |
+| **Custom Headers** | Additional HTTP headers | `{}` | - |
+
+## Supported Models
+
+QAX Autocomplete works with various AI models. Popular choices include:
+
+### Fast Models (Recommended for Autocomplete)
+- `google/gemini-2.5-flash-preview-05-20` (default)
+- `anthropic/claude-3-haiku`
+- `openai/gpt-4o-mini`
+
+### High-Quality Models
+- `anthropic/claude-3-5-sonnet`
+- `openai/gpt-4o`
+- `google/gemini-pro`
+
+### Local Models
+- Use Ollama: Set API Base URL to `http://localhost:11434/v1`
+- Use LM Studio: Set API Base URL to `http://localhost:1234/v1`
+
+## Troubleshooting
+
+### Autocomplete Not Working
+
+1. **Check if enabled**: Verify QAX Autocomplete is enabled in settings
+2. **Verify API key**: Ensure you have a valid API key configured
+3. **Check network**: Verify internet connectivity
+4. **Model availability**: Ensure the selected model is available
+5. **Check status bar**: Look for warning indicators
+
+### Common Issues
+
+#### "Warning" Status Bar
+- **Cause**: Missing or invalid API key
+- **Solution**: Configure a valid API key in settings
+
+#### No Suggestions Appearing
+- **Cause**: Model not responding or network issues
+- **Solution**: Try a different model or check network connectivity
+
+#### Slow Suggestions
+- **Cause**: Model is slow or network latency
+- **Solution**: Try a faster model or increase timeout
+
+#### High Costs
+- **Cause**: Using expensive models or high token limits
+- **Solution**: Switch to a more cost-effective model or reduce max tokens
+
+### Getting Help
+
+1. Check the VS Code Developer Console for error messages
+2. Verify your API service status
+3. Try with a different model
+4. Restart VS Code
+5. Report issues on the Cline GitHub repository
+
+## Privacy and Security
+
+- **API Keys**: Stored securely using VS Code's secret storage
+- **Code Context**: Only the immediate code context is sent to the AI service
+- **No Data Storage**: Your code is not stored by QAX Autocomplete
+- **Configurable**: You control what data is sent via your choice of API service
+
+## Cost Management
+
+- **Real-time Tracking**: Monitor costs in the status bar
+- **Session Totals**: See cumulative costs for your current session
+- **Model Selection**: Choose cost-effective models for routine work
+- **Token Limits**: Set appropriate max token limits to control costs
+
+## Best Practices
+
+1. **Start with Fast Models**: Use quick, inexpensive models for most work
+2. **Monitor Costs**: Keep an eye on the status bar cost indicator
+3. **Adjust Settings**: Fine-tune temperature and token limits for your needs
+4. **Use Appropriate Models**: Match model capability to task complexity
+5. **Secure API Keys**: Never share your API keys or commit them to version control
diff --git a/package.json b/package.json
index ec0c6bbf..8b337798 100644
--- a/package.json
+++ b/package.json
@@ -200,8 +200,90 @@
 				"command": "cline.openWalkthrough",
 				"title": "Open Walkthrough",
 				"category": "Cline"
+			},
+			{
+				"command": "qax-code.toggleAutocomplete",
+				"title": "Toggle QAX Autocomplete",
+				"category": "QAX"
+			},
+			{
+				"command": "qax-code.trackAcceptedSuggestion",
+				"title": "Track Accepted Suggestion",
+				"category": "QAX"
 			}
 		],
+		"configuration": {
+			"title": "Cline",
+			"properties": {
+				"cline.autocomplete.enabled": {
+					"type": "boolean",
+					"default": false,
+					"description": "Enable QAX autocomplete functionality"
+				},
+				"cline.autocomplete.provider": {
+					"type": "string",
+					"enum": [
+						"openai",
+						"fim"
+					],
+					"default": "openai",
+					"description": "Provider type for autocomplete service"
+				},
+				"cline.autocomplete.apiBaseUrl": {
+					"type": "string",
+					"default": "https://api.openrouter.ai/api/v1",
+					"description": "Base URL for the autocomplete API (supports any OpenAI-compatible endpoint)"
+				},
+				"cline.autocomplete.modelId": {
+					"type": "string",
+					"default": "google/gemini-2.5-flash-preview-05-20",
+					"description": "Model ID to use for autocomplete"
+				},
+				"cline.autocomplete.maxTokens": {
+					"type": "number",
+					"default": 1000,
+					"minimum": 1,
+					"maximum": 10000,
+					"description": "Maximum number of tokens for completion"
+				},
+				"cline.autocomplete.temperature": {
+					"type": "number",
+					"default": 0.1,
+					"minimum": 0,
+					"maximum": 2,
+					"description": "Temperature for completion generation"
+				},
+				"cline.autocomplete.requestTimeoutMs": {
+					"type": "number",
+					"default": 30000,
+					"minimum": 1000,
+					"maximum": 300000,
+					"description": "Request timeout in milliseconds"
+				},
+				"cline.autocomplete.usePromptCache": {
+					"type": "boolean",
+					"default": false,
+					"description": "Whether to use prompt caching"
+				},
+				"cline.autocomplete.customHeaders": {
+					"type": "object",
+					"default": {},
+					"description": "Custom headers for API requests"
+				},
+				"cline.autocomplete.debounceMs": {
+					"type": "number",
+					"default": 300,
+					"minimum": 0,
+					"maximum": 5000,
+					"description": "Debounce time in milliseconds for autocomplete requests"
+				},
+				"cline.autocomplete.fim.baseUrl": {
+					"type": "string",
+					"default": "",
+					"description": "Base URL for FIM (Fill in the Middle) API"
+				}
+			}
+		},
 		"keybindings": [
 			{
 				"command": "cline.addToChat",
@@ -315,10 +397,6 @@
 					"when": "scmProvider == git"
 				}
 			]
-		},
-		"configuration": {
-			"title": "Cline",
-			"properties": {}
 		}
 	},
 	"scripts": {
@@ -446,6 +524,7 @@
 		"grpc-health-check": "^2.0.2",
 		"iconv-lite": "^0.6.3",
 		"ignore": "^7.0.3",
+		"lru-cache": "^10.0.0",
 		"image-size": "^2.0.2",
 		"isbinaryfile": "^5.0.2",
 		"jschardet": "^3.1.4",
diff --git a/src/api/index.ts b/src/api/index.ts
index 04d346f3..93b7ea85 100644
--- a/src/api/index.ts
+++ b/src/api/index.ts
@@ -28,6 +28,7 @@ import { SambanovaHandler } from "./providers/sambanova"
 import { CerebrasHandler } from "./providers/cerebras"
 import { SapAiCoreHandler } from "./providers/sapaicore"
 import { ClaudeCodeHandler } from "./providers/claude-code"
+import { FimHandler } from "./providers/fim"
 
 export interface ApiHandler {
 	createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream
@@ -36,7 +37,8 @@ export interface ApiHandler {
 }
 
 export interface SingleCompletionHandler {
-	completePrompt(prompt: string): Promise<string>
+	completePrompt(prompt: string, suffix?: string): Promise<string>
+	completePromptStream?(prompt: string, suffix?: string): AsyncGenerator<string>
 }
 
 function createHandlerForProvider(apiProvider: string | undefined, options: any): ApiHandler {
@@ -123,3 +125,17 @@ export function buildApiHandler(configuration: ApiConfiguration): ApiHandler {
 
 	return createHandlerForProvider(apiProvider, options)
 }
+
+/**
+ * Build a FIM (Fill in the Middle) completion handler
+ */
+export function buildFimHandler(options: {
+	apiKey: string
+	baseUrl: string
+	requestTimeoutMs?: number
+	customHeaders?: Record<string, string>
+	maxTokens?: number
+	temperature?: number
+}): SingleCompletionHandler {
+	return new FimHandler(options)
+}
diff --git a/src/api/providers/fim.ts b/src/api/providers/fim.ts
new file mode 100644
index ********..4acb3edf
--- /dev/null
+++ b/src/api/providers/fim.ts
@@ -0,0 +1,386 @@
+import { SingleCompletionHandler } from "../index"
+
+/**
+ * Options for FIM (Fill in the Middle) API handler
+ */
+export interface FimHandlerOptions {
+	apiKey: string
+	baseUrl: string
+	requestTimeoutMs?: number
+	customHeaders?: Record<string, string>
+	maxTokens?: number
+	temperature?: number
+}
+
+/**
+ * FIM (Fill in the Middle) API handler for autocomplete
+ * Supports the Fill in the Middle API format with prompt, suffix, and stream parameters
+ */
+export class FimHandler implements SingleCompletionHandler {
+	private options: FimHandlerOptions
+
+	constructor(options: FimHandlerOptions) {
+		this.options = options
+	}
+
+	/**
+	 * Complete a prompt using FIM API format
+	 * @param prompt The code context before the cursor
+	 * @param suffix The code context after the cursor (optional)
+	 * @returns Promise<string> The completion text
+	 */
+	async completePrompt(prompt: string, suffix?: string): Promise<string> {
+		// Limit context size to prevent overly large requests
+		const maxContextLength = 8000 // characters
+		const limitedPrompt = this.limitContextSize(prompt, maxContextLength * 0.7)
+		const limitedSuffix = this.limitContextSize(suffix || "", maxContextLength * 0.3)
+
+		console.log(`🚀📏 FIM context sizes - prompt: ${limitedPrompt.length} chars, suffix: ${limitedSuffix.length} chars`)
+		const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`
+
+		const headers: Record<string, string> = {
+			"Content-Type": "application/json",
+			Authorization: `Bearer ${this.options.apiKey}`,
+			...this.options.customHeaders,
+		}
+
+		const body = {
+			prompt: limitedPrompt,
+			suffix: limitedSuffix,
+			stream: false, // Non-streaming for simple completion
+			max_tokens: this.options.maxTokens || 100, // Use configured max tokens
+			temperature: this.options.temperature || 0.1, // Use configured temperature
+		}
+
+		try {
+			const controller = new AbortController()
+			const timeoutId = setTimeout(() => {
+				controller.abort()
+			}, this.options.requestTimeoutMs || 30000)
+
+			const response = await fetch(url, {
+				method: "POST",
+				headers,
+				body: JSON.stringify(body),
+				signal: controller.signal,
+			})
+
+			clearTimeout(timeoutId)
+
+			if (!response.ok) {
+				const errorText = await response.text()
+				const responseHeaders: Record<string, string> = {}
+				response.headers.forEach((value, key) => {
+					responseHeaders[key] = value
+				})
+				throw new Error(`FIM API request failed:
+URL: ${url}
+HTTP Status: ${response.status} ${response.statusText}
+Response Headers: ${JSON.stringify(responseHeaders)}
+Error Message: ${errorText}`)
+			}
+
+			const data = await response.json()
+			console.log("FIM API non-streaming response:", data) // Debug log
+
+			// Extract completion text from response
+			// The exact format may vary depending on the FIM API implementation
+			if (data.choices && data.choices.length > 0) {
+				const text = data.choices[0].text || ""
+				console.log("FIM API extracted text:", text) // Debug log
+				return text
+			}
+
+			// Fallback for different response formats
+			if (data.completion) {
+				console.log("FIM API using completion field:", data.completion) // Debug log
+				return data.completion
+			}
+
+			if (data.text) {
+				console.log("FIM API using text field:", data.text) // Debug log
+				return data.text
+			}
+
+			console.warn("FIM API: No text found in response:", data) // Debug log
+			return ""
+		} catch (error) {
+			if (error instanceof Error) {
+				if (error.name === "AbortError") {
+					throw new Error(`FIM API request timed out:
+URL: ${url}
+Timeout: ${this.options.requestTimeoutMs || 30000}ms`)
+				}
+				throw new Error(`FIM API request failed:
+URL: ${url}
+Error: ${error.message}`)
+			}
+			throw new Error(`FIM API request failed with unknown error:
+URL: ${url}`)
+		}
+	}
+
+	/**
+	 * Complete a prompt with streaming support and retry mechanism
+	 * @param prompt The code context before the cursor
+	 * @param suffix The code context after the cursor (optional)
+	 * @returns AsyncGenerator<string> Stream of completion chunks
+	 */
+	async *completePromptStream(prompt: string, suffix?: string): AsyncGenerator<string> {
+		const maxRetries = 2
+		let lastError: Error | null = null
+
+		for (let attempt = 0; attempt <= maxRetries; attempt++) {
+			try {
+				console.log(`FIM API streaming attempt ${attempt + 1}/${maxRetries + 1}`)
+				yield* this.completePromptStreamInternal(prompt, suffix)
+				return // Success, exit retry loop
+			} catch (error) {
+				lastError = error as Error
+				console.warn(`FIM API streaming attempt ${attempt + 1} failed:`, error)
+
+				// Don't retry on certain errors
+				if (error instanceof Error && error.name === "AbortError") {
+					throw error // Don't retry timeout errors
+				}
+
+				// Wait before retry (exponential backoff)
+				if (attempt < maxRetries) {
+					const delay = Math.pow(2, attempt) * 1000 // 1s, 2s, 4s...
+					console.log(`FIM API retrying in ${delay}ms...`)
+					await new Promise((resolve) => setTimeout(resolve, delay))
+				}
+			}
+		}
+
+		// All retries failed
+		throw lastError || new Error("FIM API streaming failed after all retries")
+	}
+
+	/**
+	 * Internal streaming method without retry logic
+	 */
+	private async *completePromptStreamInternal(prompt: string, suffix?: string): AsyncGenerator<string> {
+		// Limit context size to prevent overly large requests
+		const maxContextLength = 8000 // characters
+		const limitedPrompt = this.limitContextSize(prompt, maxContextLength * 0.7)
+		const limitedSuffix = this.limitContextSize(suffix || "", maxContextLength * 0.3)
+
+		console.log(
+			`🚀📏 FIM streaming context sizes - prompt: ${limitedPrompt.length} chars, suffix: ${limitedSuffix.length} chars`,
+		)
+		const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`
+
+		const headers: Record<string, string> = {
+			"Content-Type": "application/json",
+			Authorization: `Bearer ${this.options.apiKey}`,
+			Accept: "text/event-stream",
+			"Cache-Control": "no-cache",
+			Connection: "keep-alive",
+			...this.options.customHeaders,
+		}
+
+		const body = {
+			prompt: limitedPrompt,
+			suffix: limitedSuffix,
+			stream: true,
+			max_tokens: this.options.maxTokens || 100,
+			temperature: this.options.temperature || 0.1,
+		}
+
+		try {
+			const controller = new AbortController()
+			const timeoutId = setTimeout(() => {
+				controller.abort()
+			}, this.options.requestTimeoutMs || 30000)
+
+			const response = await fetch(url, {
+				method: "POST",
+				headers,
+				body: JSON.stringify(body),
+				signal: controller.signal,
+			})
+
+			clearTimeout(timeoutId)
+
+			if (!response.ok) {
+				const errorText = await response.text()
+				const responseHeaders: Record<string, string> = {}
+				response.headers.forEach((value, key) => {
+					responseHeaders[key] = value
+				})
+				throw new Error(`FIM API streaming request failed:
+URL: ${url}
+HTTP Status: ${response.status} ${response.statusText}
+Response Headers: ${JSON.stringify(responseHeaders)}
+Error Message: ${errorText}`)
+			}
+
+			if (!response.body) {
+				throw new Error("No response body for streaming request")
+			}
+
+			const reader = response.body.getReader()
+			const decoder = new TextDecoder()
+			let buffer = "" // Buffer for incomplete lines
+
+			try {
+				while (true) {
+					const { done, value } = await reader.read()
+					if (done) {
+						console.log("FIM API stream completed normally")
+						break
+					}
+
+					const chunk = decoder.decode(value, { stream: true })
+					console.log("FIM API received chunk:", chunk)
+
+					// Add to buffer and split by lines
+					buffer += chunk
+					const lines = buffer.split("\n")
+
+					// Keep the last incomplete line in buffer
+					buffer = lines.pop() || ""
+
+					for (const line of lines) {
+						if (line.trim() === "") {
+							continue
+						}
+						if (line.startsWith("data: ")) {
+							const data = line.slice(6)
+							if (data === "[DONE]") {
+								continue
+							}
+
+							try {
+								const parsed = JSON.parse(data)
+								console.log("FIM API parsed response:", parsed) // Debug log
+
+								if (parsed.choices && parsed.choices.length > 0) {
+									const choice = parsed.choices[0]
+									// Handle both streaming formats: text or delta.text
+									const text = choice.text || choice.delta?.text || ""
+									if (text) {
+										console.log("FIM API yielding text:", text) // Debug log
+										yield text
+									}
+								}
+							} catch (e) {
+								console.warn("FIM API JSON parse error:", e, "Data:", data) // Debug log
+								// Skip invalid JSON lines but log the error
+								continue
+							}
+						}
+					}
+				}
+			} finally {
+				reader.releaseLock()
+			}
+		} catch (error) {
+			if (error instanceof Error) {
+				if (error.name === "AbortError") {
+					throw new Error(`FIM API streaming request timed out:
+URL: ${url}
+Timeout: ${this.options.requestTimeoutMs || 30000}ms`)
+				}
+
+				// Handle specific connection errors
+				if (error.message.includes("terminated") || error.message.includes("aborted")) {
+					throw new Error(`FIM API streaming connection terminated:
+URL: ${url}
+Error: ${error.message}
+Possible causes: Server closed connection, network instability, or proxy interference
+Suggestion: Check server logs, network connectivity, or try reducing request frequency`)
+				}
+
+				throw new Error(`FIM API streaming request failed:
+URL: ${url}
+Error: ${error.message}
+Error Type: ${error.name}
+Stack: ${error.stack}`)
+			}
+			throw new Error(`FIM API streaming request failed with unknown error:
+URL: ${url}
+Error Type: ${typeof error}
+Error Value: ${String(error)}`)
+		}
+	}
+
+	/**
+	 * Limit the size of context to prevent overly large requests
+	 * @param text The text to limit
+	 * @param maxLength Maximum length in characters
+	 * @returns Limited text
+	 */
+	private limitContextSize(text: string, maxLength: number): string {
+		if (text.length <= maxLength) {
+			return text
+		}
+
+		// For prompt (before cursor), keep the end (most recent context)
+		// For suffix (after cursor), keep the beginning
+		const lines = text.split("\n")
+		let result = ""
+		let currentLength = 0
+
+		// If this looks like a prompt (no leading whitespace on first line),
+		// keep the end (most recent context)
+		if (!text.startsWith(" ") && !text.startsWith("\t")) {
+			// Keep lines from the end
+			for (let i = lines.length - 1; i >= 0; i--) {
+				const line = lines[i]
+				if (currentLength + line.length + 1 > maxLength) {
+					break
+				}
+				result = line + (result ? "\n" + result : "")
+				currentLength += line.length + 1
+			}
+		} else {
+			// Keep lines from the beginning (for suffix)
+			for (const line of lines) {
+				if (currentLength + line.length + 1 > maxLength) {
+					break
+				}
+				result += (result ? "\n" : "") + line
+				currentLength += line.length + 1
+			}
+		}
+
+		return result
+	}
+
+	/**
+	 * Test the connection to the FIM API
+	 * @returns Promise<boolean> True if connection is healthy
+	 */
+	async testConnection(): Promise<boolean> {
+		try {
+			const url = `${this.options.baseUrl.replace(/\/$/, "")}/completions`
+			const headers: Record<string, string> = {
+				"Content-Type": "application/json",
+				Authorization: `Bearer ${this.options.apiKey}`,
+				...this.options.customHeaders,
+			}
+
+			// Send a minimal test request
+			const response = await fetch(url, {
+				method: "POST",
+				headers,
+				body: JSON.stringify({
+					prompt: "test",
+					suffix: "",
+					stream: false,
+					max_tokens: 1,
+					temperature: 0.1,
+				}),
+				signal: AbortSignal.timeout(5000), // 5 second timeout for health check
+			})
+
+			console.log(`FIM API health check: ${response.status} ${response.statusText}`)
+			return response.ok || response.status === 400 // 400 might be expected for minimal request
+		} catch (error) {
+			console.warn("FIM API health check failed:", error)
+			return false
+		}
+	}
+}
diff --git a/src/core/controller/index.ts b/src/core/controller/index.ts
index efd25b0b..3edaba7d 100644
--- a/src/core/controller/index.ts
+++ b/src/core/controller/index.ts
@@ -15,6 +15,7 @@ import { ClineAccountService } from "@services/account/ClineAccountService"
 import { McpHub } from "@services/mcp/McpHub"
 import { telemetryService } from "@/services/posthog/telemetry/TelemetryService"
 import { ApiProvider, ModelInfo } from "@shared/api"
+import { AutocompleteSettings } from "@shared/AutocompleteSettings"
 import { ChatContent } from "@shared/ChatContent"
 import { ChatSettings, StoredChatSettings } from "@shared/ChatSettings"
 import { ExtensionMessage, ExtensionState, Platform } from "@shared/ExtensionMessage"
@@ -230,6 +231,12 @@ export class Controller {
 				}
 				break
 			}
+			case "updateAutocompleteSettings": {
+				if (message.autocompleteSettings) {
+					await this.updateAutocompleteSettings(message.autocompleteSettings)
+				}
+				break
+			}
 
 			// Add more switch case statements here as more webview message commands
 			// are created within the webview context (i.e. inside media/main.js)
@@ -242,6 +249,17 @@ export class Controller {
 		telemetryService.updateTelemetryState(isOptedIn)
 	}
 
+	async updateAutocompleteSettings(autocompleteSettings: AutocompleteSettings) {
+		// Update the autocomplete config manager with new settings
+		const { AutocompleteConfigManager } = await import("../../services/autocomplete/AutocompleteConfigManager")
+		try {
+			await AutocompleteConfigManager.instance.updateSettings(autocompleteSettings)
+			console.log("QAX Autocomplete settings updated successfully")
+		} catch (error) {
+			console.error("Failed to update QAX Autocomplete settings:", error)
+		}
+	}
+
 	async togglePlanActModeWithChatSettings(chatSettings: ChatSettings, chatContent?: ChatContent): Promise<boolean> {
 		const didSwitchToActMode = chatSettings.mode === "act"
 
@@ -873,6 +891,17 @@ export class Controller {
 
 		const localWorkflowToggles = ((await getWorkspaceState(this.context, "workflowToggles")) as ClineRulesToggles) || {}
 
+		// Get autocomplete settings
+		let autocompleteSettings: AutocompleteSettings
+		try {
+			const { AutocompleteConfigManager } = await import("../../services/autocomplete/AutocompleteConfigManager")
+			autocompleteSettings = AutocompleteConfigManager.instance.getSettings()
+		} catch (error) {
+			// If AutocompleteConfigManager is not initialized, use default settings
+			const { DEFAULT_AUTOCOMPLETE_SETTINGS } = await import("../../shared/AutocompleteSettings")
+			autocompleteSettings = DEFAULT_AUTOCOMPLETE_SETTINGS
+		}
+
 		return {
 			version: this.context.extension?.packageJSON?.version ?? "",
 			apiConfiguration,
@@ -887,6 +916,7 @@ export class Controller {
 			shouldShowAnnouncement: lastShownAnnouncementId !== this.latestAnnouncementId,
 			platform: process.platform as Platform,
 			autoApprovalSettings,
+			autocompleteSettings,
 			browserSettings,
 			chatSettings,
 			userInfo,
diff --git a/src/extension.ts b/src/extension.ts
index 5465b038..e9fc7cca 100644
--- a/src/extension.ts
+++ b/src/extension.ts
@@ -34,6 +34,7 @@ import * as hostProviders from "@hosts/host-providers"
 import { vscodeHostBridgeClient } from "@/hosts/vscode/client/host-grpc-client"
 import { VscodeWebviewProvider } from "./core/webview/VscodeWebviewProvider"
 import { ExtensionContext } from "vscode"
+import { registerAutocomplete } from "./services/autocomplete/AutocompleteProvider"
 
 /*
 Built using https://github.com/microsoft/vscode-webview-ui-toolkit
@@ -658,6 +659,14 @@ export async function activate(context: vscode.ExtensionContext) {
 		}),
 	)
 
+	// Initialize autocomplete functionality
+	try {
+		registerAutocomplete(context)
+		Logger.log("QAX Autocomplete initialized successfully")
+	} catch (error) {
+		Logger.log(`Failed to initialize QAX Autocomplete: ${error}`)
+	}
+
 	return createClineAPI(outputChannel, sidebarWebview.controller)
 }
 
diff --git a/src/services/autocomplete/AutocompleteConfigManager.ts b/src/services/autocomplete/AutocompleteConfigManager.ts
new file mode 100644
index ********..2c115734
--- /dev/null
+++ b/src/services/autocomplete/AutocompleteConfigManager.ts
@@ -0,0 +1,174 @@
+import * as vscode from "vscode"
+import { AutocompleteSettings, DEFAULT_AUTOCOMPLETE_SETTINGS } from "../../shared/AutocompleteSettings"
+
+/**
+ * Configuration manager for autocomplete settings
+ * Replaces the ContextProxy dependency from the original autocomplete module
+ */
+export class AutocompleteConfigManager {
+	private static _instance: AutocompleteConfigManager | null = null
+	private _context: vscode.ExtensionContext
+	private _settings: AutocompleteSettings
+
+	private constructor(context: vscode.ExtensionContext) {
+		this._context = context
+		this._settings = this.loadSettings()
+	}
+
+	public static initialize(context: vscode.ExtensionContext): void {
+		if (!AutocompleteConfigManager._instance) {
+			AutocompleteConfigManager._instance = new AutocompleteConfigManager(context)
+		}
+	}
+
+	public static get instance(): AutocompleteConfigManager {
+		if (!AutocompleteConfigManager._instance) {
+			throw new Error("AutocompleteConfigManager must be initialized before use")
+		}
+		return AutocompleteConfigManager._instance
+	}
+
+	/**
+	 * Load autocomplete settings from VS Code configuration and global state
+	 */
+	private loadSettings(): AutocompleteSettings {
+		const config = vscode.workspace.getConfiguration("cline.autocomplete")
+		const globalState = this._context.globalState
+
+		return {
+			enabled: config.get("enabled", DEFAULT_AUTOCOMPLETE_SETTINGS.enabled),
+			provider: config.get("provider", DEFAULT_AUTOCOMPLETE_SETTINGS.provider),
+			apiKey: globalState.get("autocompleteApiKey") || config.get("apiKey"),
+			apiBaseUrl: config.get("apiBaseUrl", DEFAULT_AUTOCOMPLETE_SETTINGS.apiBaseUrl),
+			modelId: config.get("modelId", DEFAULT_AUTOCOMPLETE_SETTINGS.modelId),
+			maxTokens: config.get("maxTokens", DEFAULT_AUTOCOMPLETE_SETTINGS.maxTokens),
+			temperature: config.get("temperature", DEFAULT_AUTOCOMPLETE_SETTINGS.temperature),
+			requestTimeoutMs: config.get("requestTimeoutMs", DEFAULT_AUTOCOMPLETE_SETTINGS.requestTimeoutMs),
+			usePromptCache: config.get("usePromptCache", DEFAULT_AUTOCOMPLETE_SETTINGS.usePromptCache),
+			customHeaders: config.get("customHeaders", DEFAULT_AUTOCOMPLETE_SETTINGS.customHeaders),
+			debounceMs: config.get("debounceMs", DEFAULT_AUTOCOMPLETE_SETTINGS.debounceMs),
+			fim: {
+				apiKey:
+					globalState.get("autocompleteFimApiKey") ||
+					config.get("fim.apiKey", DEFAULT_AUTOCOMPLETE_SETTINGS.fim?.apiKey),
+				baseUrl: config.get("fim.baseUrl", DEFAULT_AUTOCOMPLETE_SETTINGS.fim?.baseUrl),
+			},
+		}
+	}
+
+	/**
+	 * Get current autocomplete settings
+	 */
+	public getSettings(): AutocompleteSettings {
+		// Reload settings to get latest values
+		this._settings = this.loadSettings()
+		return { ...this._settings }
+	}
+
+	/**
+	 * Update autocomplete settings
+	 */
+	public async updateSettings(settings: Partial<AutocompleteSettings>): Promise<void> {
+		const config = vscode.workspace.getConfiguration("cline.autocomplete")
+		const globalState = this._context.globalState
+
+		// Update configuration values
+		if (settings.enabled !== undefined) {
+			await config.update("enabled", settings.enabled, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.provider !== undefined) {
+			await config.update("provider", settings.provider, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.apiKey !== undefined) {
+			await globalState.update("autocompleteApiKey", settings.apiKey)
+		}
+		if (settings.apiBaseUrl !== undefined) {
+			await config.update("apiBaseUrl", settings.apiBaseUrl, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.modelId !== undefined) {
+			await config.update("modelId", settings.modelId, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.maxTokens !== undefined) {
+			await config.update("maxTokens", settings.maxTokens, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.temperature !== undefined) {
+			await config.update("temperature", settings.temperature, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.requestTimeoutMs !== undefined) {
+			await config.update("requestTimeoutMs", settings.requestTimeoutMs, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.usePromptCache !== undefined) {
+			await config.update("usePromptCache", settings.usePromptCache, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.customHeaders !== undefined) {
+			await config.update("customHeaders", settings.customHeaders, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.debounceMs !== undefined) {
+			await config.update("debounceMs", settings.debounceMs, vscode.ConfigurationTarget.Global)
+		}
+		if (settings.fim?.apiKey !== undefined) {
+			await globalState.update("autocompleteFimApiKey", settings.fim.apiKey)
+		}
+		if (settings.fim?.baseUrl !== undefined) {
+			await config.update("fim.baseUrl", settings.fim.baseUrl, vscode.ConfigurationTarget.Global)
+		}
+
+		// Reload settings
+		this._settings = this.loadSettings()
+	}
+
+	/**
+	 * Get provider settings in the format expected by the original autocomplete code
+	 * This method provides compatibility with the original ContextProxy.getProviderSettings() method
+	 */
+	public getProviderSettings(): { qaxToken?: string; qaxModel?: string; qaxBaseUrl?: string } {
+		const settings = this.getSettings()
+		return {
+			qaxToken: settings.apiKey,
+			qaxModel: settings.modelId,
+			qaxBaseUrl: settings.apiBaseUrl,
+		}
+	}
+
+	/**
+	 * Get global state value
+	 * This method provides compatibility with the original ContextProxy.getGlobalState() method
+	 */
+	public getGlobalState(key: string): any {
+		if (key === "experiments") {
+			// Return autocomplete experiment flag based on enabled setting
+			return {
+				autocomplete: this.getSettings().enabled,
+			}
+		}
+		return this._context.globalState.get(key)
+	}
+
+	/**
+	 * Set global state value
+	 */
+	public async setGlobalState(key: string, value: any): Promise<void> {
+		await this._context.globalState.update(key, value)
+	}
+
+	/**
+	 * Check if autocomplete is enabled
+	 */
+	public isEnabled(): boolean {
+		return this.getSettings().enabled
+	}
+
+	/**
+	 * Check if API key is configured based on the selected provider
+	 */
+	public hasApiKey(): boolean {
+		const settings = this.getSettings()
+
+		if (settings.provider === "fim") {
+			return !!(settings.fim?.apiKey && settings.fim.apiKey.trim() && settings.fim?.baseUrl && settings.fim.baseUrl.trim())
+		} else {
+			// Default to OpenAI provider
+			return !!(settings.apiKey && settings.apiKey.trim())
+		}
+	}
+}
diff --git a/src/services/autocomplete/AutocompleteDecorationAnimation.ts b/src/services/autocomplete/AutocompleteDecorationAnimation.ts
new file mode 100644
index ********..fa11d964
--- /dev/null
+++ b/src/services/autocomplete/AutocompleteDecorationAnimation.ts
@@ -0,0 +1,170 @@
+import * as vscode from "vscode"
+
+export const UI_SHOW_LOADING_DELAY_MS = 150
+
+/**
+ * Manages the animated decoration for autocomplete loading indicator
+ */
+export class AutocompleteDecorationAnimation {
+	private static instance: AutocompleteDecorationAnimation
+	private animationInitialWaitTimer: NodeJS.Timeout | null = null
+	private animationInterval: NodeJS.Timeout | null = null
+	private decorationType: vscode.TextEditorDecorationType
+	private animationState = 0
+	private isTypingPhase = true // Track whether we're in typing phase or blinking phase
+	private readonly animationFrames = ["█", "C█", "Co█", "Cod█", "Code█", "CodeG█", "CodeGe█", "CodeGen█"]
+	private isBlockVisible = true // For blinking effect when fully spelled
+	private editor: vscode.TextEditor | null = null
+	private range: vscode.Range | null = null
+
+	private constructor() {
+		this.decorationType = vscode.window.createTextEditorDecorationType({
+			after: {
+				color: new vscode.ThemeColor("editorGhostText.foreground"),
+				fontStyle: "italic",
+				contentText: "⏳", // Initial state before animation starts
+			},
+			rangeBehavior: vscode.DecorationRangeBehavior.ClosedOpen,
+		})
+	}
+
+	public static getInstance(): AutocompleteDecorationAnimation {
+		if (!AutocompleteDecorationAnimation.instance) {
+			AutocompleteDecorationAnimation.instance = new AutocompleteDecorationAnimation()
+		}
+		return AutocompleteDecorationAnimation.instance
+	}
+
+	/**
+	 * Starts the loading animation at the specified range in the editor
+	 */
+	public startAnimation(): void {
+		const editor = vscode.window.activeTextEditor
+		if (!editor) return
+
+		this.stopAnimation() // Stop any existing animation
+
+		const position = editor.selection.active
+		const document = editor.document
+		const lineEndPosition = new vscode.Position(position.line, document.lineAt(position.line).text.length)
+
+		this.editor = editor
+		this.range = new vscode.Range(lineEndPosition, lineEndPosition)
+		this.animationState = 0
+		this.isTypingPhase = true // Reset to typing phase
+		this.isBlockVisible = true
+
+		// Delay starting the animation slightly to not distract users
+		// We're still fetching the completion, this just delays showing the decorator.
+		this.animationInitialWaitTimer = setTimeout(() => {
+			// Apply initial animation state
+			this.updateDecorationText()
+
+			// Start animation interval
+			this.animationInterval = setInterval(() => {
+				this.updateAnimation()
+			}, 100)
+		}, UI_SHOW_LOADING_DELAY_MS)
+	}
+
+	/**
+	 * Stops the loading animation and immediately hides the decorator
+	 */
+	public stopAnimation(): void {
+		// Clear animation immediately
+		if (this.animationInterval) {
+			clearInterval(this.animationInterval)
+			this.animationInterval = null
+		}
+
+		if (this.animationInitialWaitTimer) {
+			clearTimeout(this.animationInitialWaitTimer)
+		}
+
+		if (this.editor && this.decorationType) {
+			this.editor.setDecorations(this.decorationType, [])
+		}
+
+		this.editor = null
+		this.range = null
+	}
+
+	/**
+	 * Updates the animation state and decoration text
+	 */
+	private updateAnimation(): void {
+		if (!this.editor || !this.range) {
+			this.stopAnimation()
+			return
+		}
+
+		// Animation with two phases:
+		// 1. Typing out "CodeGen" (block moves to the right) - faster (100ms)
+		// 2. Blinking block at the end when fully spelled - slower (200ms)
+		if (this.animationState < this.animationFrames.length - 1) {
+			// Phase 1: Spell out "CodeGen" with block cursor
+			this.animationState++
+		} else {
+			// Check if we just reached the end of typing phase
+			if (this.isTypingPhase) {
+				// Transition from typing to blinking phase
+				this.isTypingPhase = false
+
+				// Clear current interval and create a new one with slower timing (200ms)
+				if (this.animationInterval) {
+					clearInterval(this.animationInterval)
+				}
+
+				this.animationInterval = setInterval(() => {
+					this.updateAnimation()
+				}, 200)
+			}
+
+			// Phase 2: Blink the block cursor at the end
+			this.isBlockVisible = !this.isBlockVisible
+		}
+
+		this.updateDecorationText()
+	}
+
+	/**
+	 * Updates the decoration text based on current animation state
+	 */
+	private updateDecorationText(): void {
+		if (!this.editor || !this.range) return
+
+		let text
+
+		// When fully spelled and in blinking mode
+		if (this.animationState === this.animationFrames.length - 1) {
+			// Show either the full frame with block, or just "CodeGen" without block
+			text = this.isBlockVisible ? this.animationFrames[this.animationState] : "CodeGen"
+		} else {
+			// Normal animation frames (with block)
+			text = this.animationFrames[this.animationState]
+		}
+
+		// Update decoration type with new text
+		const updatedDecorationType = vscode.window.createTextEditorDecorationType({
+			after: {
+				color: new vscode.ThemeColor("editorGhostText.foreground"),
+				fontStyle: "italic",
+				contentText: text,
+			},
+			rangeBehavior: vscode.DecorationRangeBehavior.ClosedOpen,
+		})
+
+		// Apply updated decoration
+		this.editor.setDecorations(this.decorationType, [])
+		this.decorationType = updatedDecorationType
+		this.editor.setDecorations(this.decorationType, [this.range])
+	}
+
+	/**
+	 * Disposes the decoration type and stops any active animation
+	 */
+	public dispose(): void {
+		this.stopAnimation()
+		this.decorationType?.dispose()
+	}
+}
diff --git a/src/services/autocomplete/AutocompleteLanguageInfo.ts b/src/services/autocomplete/AutocompleteLanguageInfo.ts
new file mode 100644
index ********..994c487e
--- /dev/null
+++ b/src/services/autocomplete/AutocompleteLanguageInfo.ts
@@ -0,0 +1,110 @@
+//PLANREF: continue/core/autocomplete/constants/AutocompleteLanguageInfo.ts
+/**
+ * Interface for language-specific autocomplete information
+ */
+export interface AutocompleteLanguageInfo {
+	name: string
+	singleLineComment: string
+	topLevelKeywords: string[]
+}
+
+/**
+ * Get the language info for a specific language ID
+ * @param languageId Language ID
+ * @returns Language info
+ */
+export function getLanguageInfo(languageId: string): AutocompleteLanguageInfo {
+	switch (languageId.toLowerCase()) {
+		case "typescript":
+		case "javascript":
+		case "tsx":
+		case "jsx":
+			return {
+				name: languageId,
+				singleLineComment: "//",
+				topLevelKeywords: ["function", "class", "const", "let", "var", "import", "export"],
+			}
+		case "python":
+			return {
+				name: languageId,
+				singleLineComment: "#",
+				topLevelKeywords: ["def", "class", "import", "from", "if", "for", "while"],
+			}
+		case "java":
+		case "c":
+		case "cpp":
+		case "csharp":
+		case "go":
+			return {
+				name: languageId,
+				singleLineComment: "//",
+				topLevelKeywords: ["class", "interface", "function", "struct", "enum"],
+			}
+		case "ruby":
+			return {
+				name: languageId,
+				singleLineComment: "#",
+				topLevelKeywords: ["def", "class", "module", "require"],
+			}
+		case "rust":
+			return {
+				name: languageId,
+				singleLineComment: "//",
+				topLevelKeywords: ["fn", "struct", "enum", "impl", "trait", "mod", "use"],
+			}
+		case "php":
+			return {
+				name: languageId,
+				singleLineComment: "//",
+				topLevelKeywords: ["function", "class", "namespace", "require", "include"],
+			}
+		default:
+			return {
+				name: languageId,
+				singleLineComment: "//",
+				topLevelKeywords: [],
+			}
+	}
+}
+
+/**
+ * Get language info for a file path
+ * @param filepath File path
+ * @returns Language info
+ */
+export function languageForFilepath(filepath: string): AutocompleteLanguageInfo {
+	const ext = filepath.split(".").pop()?.toLowerCase() || ""
+
+	switch (ext) {
+		case "ts":
+			return getLanguageInfo("typescript")
+		case "js":
+			return getLanguageInfo("javascript")
+		case "tsx":
+			return getLanguageInfo("tsx")
+		case "jsx":
+			return getLanguageInfo("jsx")
+		case "py":
+			return getLanguageInfo("python")
+		case "java":
+			return getLanguageInfo("java")
+		case "c":
+			return getLanguageInfo("c")
+		case "cpp":
+		case "cc":
+		case "cxx":
+			return getLanguageInfo("cpp")
+		case "cs":
+			return getLanguageInfo("csharp")
+		case "go":
+			return getLanguageInfo("go")
+		case "rb":
+			return getLanguageInfo("ruby")
+		case "rs":
+			return getLanguageInfo("rust")
+		case "php":
+			return getLanguageInfo("php")
+		default:
+			return getLanguageInfo("")
+	}
+}
diff --git a/src/services/autocomplete/AutocompleteProvider.ts b/src/services/autocomplete/AutocompleteProvider.ts
new file mode 100644
index ********..a6837c87
--- /dev/null
+++ b/src/services/autocomplete/AutocompleteProvider.ts
@@ -0,0 +1,528 @@
+import * as vscode from "vscode"
+import { buildApiHandler, buildFimHandler, ApiHandler, SingleCompletionHandler } from "../../api"
+import { CodeContext, ContextGatherer } from "./ContextGatherer"
+import { holeFillerTemplate } from "./templating/AutocompleteTemplate"
+import { AutocompleteConfigManager } from "./AutocompleteConfigManager"
+import { generateImportSnippets, generateDefinitionSnippets } from "./context/snippetProvider"
+import { LRUCache } from "lru-cache"
+import { createDebouncedFn } from "./utils/createDebouncedFn"
+import { AutocompleteDecorationAnimation } from "./AutocompleteDecorationAnimation"
+import { isHumanEdit } from "./utils/EditDetectionUtils"
+
+export const UI_UPDATE_DEBOUNCE_MS = 250
+export const BAIL_OUT_TOO_MANY_LINES_LIMIT = 1000
+export const MAX_COMPLETIONS_PER_CONTEXT = 5 // Per-given prefix/suffix lines, how many different per-line options to cache
+
+// const DEFAULT_MODEL = "mistralai/codestral-2501"
+const DEFAULT_MODEL = "DeepSeek-V3-0324"
+
+export function processModelResponse(responseText: string): string {
+	const fullMatch = /(<COMPLETION>)?([\s\S]*?)(<\/COMPLETION>|$)/.exec(responseText)
+	if (!fullMatch) {
+		return responseText
+	}
+	if (fullMatch[2].endsWith("</COMPLETION>")) {
+		return fullMatch[2].slice(0, -"</COMPLETION>".length)
+	}
+	return fullMatch[2]
+}
+
+/**
+ * Generates a cache key based on context's preceding and following lines
+ * This is used to identify when we can reuse a previous completion
+ */
+function generateCacheKey({ precedingLines, followingLines }: CodeContext): string {
+	const maxLinesToConsider = 5
+	const precedingContext = precedingLines.slice(-maxLinesToConsider).join("\n")
+	const followingContext = followingLines.slice(0, maxLinesToConsider).join("\n")
+	return `${precedingContext}|||${followingContext}`
+}
+
+/**
+ * Sets up autocomplete with configuration checking.
+ * This function periodically checks the configuration and registers/disposes
+ * the autocomplete provider accordingly.
+ */
+export function registerAutocomplete(context: vscode.ExtensionContext): void {
+	// Initialize the config manager
+	AutocompleteConfigManager.initialize(context)
+
+	let autocompleteDisposable: vscode.Disposable | null = null
+	let isCurrentlyEnabled = false
+
+	// Function to check configuration and update provider
+	const checkAndUpdateProvider = () => {
+		const configManager = AutocompleteConfigManager.instance
+		const shouldBeEnabled = configManager.isEnabled() && configManager.hasApiKey()
+
+		// Only take action if the state has changed
+		if (shouldBeEnabled !== isCurrentlyEnabled) {
+			console.log(`🚀🔍 QAX Autocomplete enabled state changed to: ${shouldBeEnabled}`)
+
+			autocompleteDisposable?.dispose()
+			autocompleteDisposable = shouldBeEnabled ? setupAutocomplete(context) : null
+			isCurrentlyEnabled = shouldBeEnabled
+		}
+	}
+
+	checkAndUpdateProvider()
+	const configCheckInterval = setInterval(checkAndUpdateProvider, 5000)
+
+	// Make sure to clean up the interval when the extension is deactivated
+	context.subscriptions.push({
+		dispose: () => {
+			clearInterval(configCheckInterval)
+			autocompleteDisposable?.dispose()
+		},
+	})
+}
+
+function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable {
+	// State
+	let enabled = true // User toggle state (default to enabled)
+	let activeRequestId: string | null = null
+	let isBackspaceOperation = false // Flag to track backspace operations
+	let justAcceptedSuggestion = false // Flag to track if a suggestion was just accepted
+	let totalCompletionCalls = 0 // Track the total number of completion calls
+	let totalAcceptedSuggestions = 0 // Track the total number of accepted suggestions
+
+	// LRU Cache for completions
+	const completionsCache = new LRUCache<string, string[]>({
+		max: 50,
+		ttl: 1000 * 60 * 60 * 24, // Cache for 24 hours
+	})
+
+	// Services
+	const contextGatherer = new ContextGatherer()
+	const animationManager = AutocompleteDecorationAnimation.getInstance()
+
+	// Initialize API handler based on provider type
+	let apiHandler: ApiHandler | null = null
+	let fimHandler: SingleCompletionHandler | null = null
+	const configManager = AutocompleteConfigManager.instance
+	const settings = configManager.getSettings()
+
+	if (settings.provider === "fim" && settings.fim?.apiKey && settings.fim?.baseUrl) {
+		fimHandler = buildFimHandler({
+			apiKey: settings.fim.apiKey,
+			baseUrl: settings.fim.baseUrl,
+			requestTimeoutMs: settings.requestTimeoutMs,
+			customHeaders: settings.customHeaders,
+			maxTokens: settings.maxTokens,
+			temperature: settings.temperature,
+		})
+	} else if (settings.apiKey) {
+		apiHandler = buildApiHandler({
+			apiProvider: "openai", // Use OpenAI-compatible interface
+			openAiApiKey: settings.apiKey,
+			openAiBaseUrl: settings.apiBaseUrl || "https://aip.b.qianxin-inc.cn/v2",
+			openAiModelId: settings.modelId || DEFAULT_MODEL,
+			requestTimeoutMs: settings.requestTimeoutMs,
+		})
+	}
+
+	const clearState = () => {
+		vscode.commands.executeCommand("editor.action.inlineSuggest.hide")
+		animationManager.stopAnimation()
+
+		isBackspaceOperation = false
+		justAcceptedSuggestion = false
+		activeRequestId = null
+	}
+
+	const generateFimCompletion = async ({
+		codeContext: _codeContext,
+		document,
+		position,
+	}: {
+		codeContext: CodeContext
+		document: vscode.TextDocument
+		position: vscode.Position
+	}) => {
+		if (!fimHandler) {
+			throw new Error("fimHandler must be set before calling generateFimCompletion!")
+		}
+
+		const requestId = crypto.randomUUID()
+		activeRequestId = requestId
+		animationManager.startAnimation()
+
+		// Get text before and after cursor for FIM (limited to 100 lines total)
+		const maxLines = 100
+		const currentLine = position.line
+
+		// Calculate how many lines to include before and after cursor
+		const linesBeforeLimit = Math.min(currentLine, Math.floor(maxLines * 0.7)) // 70% for context before
+		const linesAfterLimit = Math.min(document.lineCount - currentLine - 1, maxLines - linesBeforeLimit)
+
+		// Get limited text before cursor
+		const startLine = Math.max(0, currentLine - linesBeforeLimit)
+		const textBeforeCursor = document.getText(new vscode.Range(new vscode.Position(startLine, 0), position))
+
+		// Get limited text after cursor
+		const endLine = Math.min(document.lineCount, currentLine + linesAfterLimit + 1)
+		const textAfterCursor = document.getText(new vscode.Range(position, new vscode.Position(endLine, 0)))
+
+		console.log(
+			`🚀📏 FIM context: ${linesBeforeLimit} lines before, ${linesAfterLimit} lines after (total: ${linesBeforeLimit + linesAfterLimit} lines)`,
+		)
+
+		try {
+			let completion = ""
+
+			// Use streaming if available, otherwise fall back to non-streaming
+			if (fimHandler.completePromptStream) {
+				console.log("🚀🔄 Starting FIM streaming completion...")
+				// Use streaming for better real-time experience
+				for await (const chunk of fimHandler.completePromptStream(textBeforeCursor, textAfterCursor)) {
+					if (activeRequestId !== requestId) {
+						console.log("🚀⏹️ FIM completion cancelled (request no longer active)")
+						return null // This request is no longer active
+					}
+					completion += chunk
+					console.log(`🚀📝 FIM chunk received: "${chunk}", total: "${completion}"`)
+				}
+				console.log("🚀✅ FIM streaming completion finished")
+			} else {
+				console.log("🚀📞 Using FIM non-streaming completion...")
+				// Fall back to non-streaming
+				completion = await fimHandler.completePrompt(textBeforeCursor, textAfterCursor)
+			}
+
+			if (activeRequestId !== requestId) {
+				return null // This request is no longer active
+			}
+
+			const processedCompletion = processModelResponse(completion)
+
+			console.log(`🚀🎯 FIM completion generated: "${processedCompletion}"`)
+			totalCompletionCalls++
+			updateStatusBar()
+
+			return processedCompletion
+		} catch (error) {
+			console.error("🚀❌ FIM completion error:", error)
+			return null
+		} finally {
+			animationManager.stopAnimation()
+		}
+	}
+
+	const generateCompletion = async ({
+		codeContext,
+		document,
+		position,
+	}: {
+		codeContext: CodeContext
+		document: vscode.TextDocument
+		position: vscode.Position
+	}) => {
+		if (!apiHandler) {
+			throw new Error("apiHandler must be set before calling generateCompletion!")
+		}
+
+		const requestId = crypto.randomUUID()
+		activeRequestId = requestId
+		animationManager.startAnimation()
+
+		const snippets = [
+			...generateImportSnippets(true, codeContext.imports, document.uri.fsPath),
+			...generateDefinitionSnippets(true, codeContext.definitions),
+		]
+		const systemPrompt = holeFillerTemplate.getSystemPrompt()
+		const userPrompt = holeFillerTemplate.template(codeContext, document, position, snippets)
+
+		console.log(`🚀🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶\n`, { userPrompt })
+
+		const stream = apiHandler.createMessage(systemPrompt, [{ role: "user", content: [{ type: "text", text: userPrompt }] }])
+
+		let completion = ""
+		let processedCompletion = ""
+		let lineCount = 0
+		let completionCost = 0
+
+		try {
+			for await (const chunk of stream) {
+				if (activeRequestId !== requestId) {
+					break // This request is no longer active
+				}
+
+				if (chunk.type === "text") {
+					completion += chunk.text
+					processedCompletion = processModelResponse(completion)
+					lineCount += processedCompletion.split("/n").length
+				} else if (chunk.type === "usage") {
+					completionCost = chunk.totalCost ?? 0
+				}
+
+				if (lineCount > BAIL_OUT_TOO_MANY_LINES_LIMIT) {
+					processedCompletion = ""
+					break
+				}
+			}
+		} catch (error) {
+			console.error("Error streaming completion:", error)
+			processedCompletion = ""
+		}
+
+		// Update completion call tracking
+		totalCompletionCalls++
+		console.log(`🚀📊 Completion generated (${totalCompletionCalls} total calls)`)
+
+		// Update status bar with call information
+		updateStatusBar()
+
+		if (activeRequestId === requestId) {
+			animationManager.stopAnimation()
+		}
+
+		return { processedCompletion, lineCount, cost: completionCost }
+	}
+
+	// Get debounce time from settings
+	const getDebounceMs = () => {
+		const configManager = AutocompleteConfigManager.instance
+		const settings = configManager.getSettings()
+		return settings.debounceMs || UI_UPDATE_DEBOUNCE_MS
+	}
+
+	const debouncedGenerateCompletion = createDebouncedFn(generateCompletion, getDebounceMs())
+	const debouncedGenerateFimCompletion = createDebouncedFn(generateFimCompletion, getDebounceMs())
+
+	const provider: vscode.InlineCompletionItemProvider = {
+		async provideInlineCompletionItems(document, position, _context, token) {
+			if (!enabled || !vscode.window.activeTextEditor) {
+				return null
+			}
+
+			const configManager = AutocompleteConfigManager.instance
+			const settings = configManager.getSettings()
+
+			// Check if we have valid configuration based on provider
+			if (settings.provider === "fim") {
+				if (!settings.fim?.apiKey || !settings.fim?.baseUrl) {
+					return null
+				}
+				// Create or recreate the FIM handler if needed
+				fimHandler =
+					fimHandler ??
+					buildFimHandler({
+						apiKey: settings.fim.apiKey,
+						baseUrl: settings.fim.baseUrl,
+						requestTimeoutMs: settings.requestTimeoutMs,
+						customHeaders: settings.customHeaders,
+						maxTokens: settings.maxTokens,
+						temperature: settings.temperature,
+					})
+			} else {
+				if (!settings.apiKey) {
+					return null
+				}
+				// Create or recreate the API handler if needed
+				apiHandler =
+					apiHandler ??
+					buildApiHandler({
+						apiProvider: "openai", // Use OpenAI-compatible interface
+						openAiApiKey: settings.apiKey,
+						openAiBaseUrl: settings.apiBaseUrl || "https://aip.b.qianxin-inc.cn/v2",
+						openAiModelId: settings.modelId || DEFAULT_MODEL,
+						requestTimeoutMs: settings.requestTimeoutMs,
+					})
+			}
+
+			// Skip providing completions if this was triggered by a backspace operation of if we just accepted a suggestion
+			if (isBackspaceOperation || justAcceptedSuggestion) {
+				return null
+			}
+
+			// Get exactly what's been typed on the current line
+			const linePrefix = document.getText(new vscode.Range(new vscode.Position(position.line, 0), position)).trimStart()
+			console.log(`🚀🛑 Autocomplete for line with prefix: "${linePrefix}"!`)
+
+			const codeContext = await contextGatherer.gatherContext(document, position, true, true)
+
+			// Check if we have a cached completion for this context
+			const cacheKey = generateCacheKey(codeContext)
+			const cachedCompletions = completionsCache.get(cacheKey) ?? []
+			for (const completion of cachedCompletions) {
+				if (completion.startsWith(linePrefix)) {
+					// Only show the remaining part of the completion
+					const remainingSuffix = completion.substring(linePrefix.length)
+					if (remainingSuffix.length > 0) {
+						console.log(`🚀🎯 Using cached completions (${cachedCompletions.length} options)`)
+						animationManager.stopAnimation()
+						return [createInlineCompletionItem(remainingSuffix, position)]
+					}
+				}
+			}
+
+			let processedCompletion: string
+
+			if (settings.provider === "fim") {
+				const result = await debouncedGenerateFimCompletion({ document, codeContext, position })
+				if (!result || token.isCancellationRequested) {
+					return null
+				}
+				processedCompletion = result
+			} else {
+				const result = await debouncedGenerateCompletion({ document, codeContext, position })
+				if (!result || token.isCancellationRequested) {
+					return null
+				}
+				processedCompletion = result.processedCompletion
+			}
+			console.log(`🚀🛑🚀🛑🚀🛑🚀🛑🚀🛑 \n`, {
+				processedCompletion,
+			})
+
+			// Cache the successful completion for future use
+			if (processedCompletion) {
+				const completions = completionsCache.get(cacheKey) ?? []
+
+				// Add the new completion if it's not already in the list
+				if (!completions.includes(processedCompletion)) {
+					completions.push(linePrefix + processedCompletion)
+					console.log(`🚀🛑 Saved new cache entry '${linePrefix + processedCompletion}'`)
+
+					// Prune the array if it exceeds the maximum size
+					// Keep the most recent completions (remove from the beginning)
+					if (completions.length > MAX_COMPLETIONS_PER_CONTEXT) {
+						completions.splice(0, completions.length - MAX_COMPLETIONS_PER_CONTEXT)
+					}
+				}
+				completionsCache.set(cacheKey, completions)
+			}
+
+			return [createInlineCompletionItem(processedCompletion, position)]
+		},
+	}
+
+	// Register provider and commands
+	const providerDisposable = vscode.languages.registerInlineCompletionItemProvider({ pattern: "**" }, provider)
+
+	// Status bar
+	const statusBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100)
+	statusBar.text = "$(sparkle) QAX Complete"
+	statusBar.tooltip = "QAX Code Autocomplete"
+	statusBar.command = "qax-code.toggleAutocomplete"
+	statusBar.show()
+
+	// Helper function to update status bar with call and acceptance statistics
+	const updateStatusBar = () => {
+		if (!enabled) {
+			statusBar.text = "$(circle-slash) QAX Complete"
+			statusBar.tooltip = "QAX Code Autocomplete (disabled)"
+			return
+		}
+
+		// Check if API key is set
+		const configManager = AutocompleteConfigManager.instance
+		const settings = configManager.getSettings()
+		if (!settings.apiKey) {
+			statusBar.text = "$(warning) QAX Complete"
+			statusBar.tooltip = "A valid API key must be set to use autocomplete"
+			return
+		}
+
+		const acceptanceRate =
+			totalCompletionCalls > 0 ? ((totalAcceptedSuggestions / totalCompletionCalls) * 100).toFixed(1) : "0.0"
+		statusBar.text = `$(sparkle) QAX Complete (${totalAcceptedSuggestions}/${totalCompletionCalls})`
+		statusBar.tooltip = `\
+QAX Code Autocomplete
+
+Calls: ${totalCompletionCalls}
+Accepted: ${totalAcceptedSuggestions}
+Acceptance Rate: ${acceptanceRate}%
+Model: ${settings.modelId || DEFAULT_MODEL}\
+`
+	}
+
+	const toggleCommand = vscode.commands.registerCommand("qax-code.toggleAutocomplete", () => {
+		enabled = !enabled
+		updateStatusBar()
+		vscode.window.showInformationMessage(`QAX Complete ${enabled ? "enabled" : "disabled"}`)
+	})
+
+	// Command to track when a suggestion is accepted
+	const trackAcceptedSuggestionCommand = vscode.commands.registerCommand("qax-code.trackAcceptedSuggestion", () => {
+		justAcceptedSuggestion = true
+		totalAcceptedSuggestions++
+		console.log(`🚀✅ Suggestion accepted (${totalAcceptedSuggestions} total accepted)`)
+		updateStatusBar()
+	})
+
+	// Event handlers
+	const selectionHandler = vscode.window.onDidChangeTextEditorSelection((_e) => {
+		// Reset the flag when selection changes
+		// This ensures we only skip one completion request after accepting a suggestion
+		justAcceptedSuggestion = false
+	})
+	const documentHandler = vscode.workspace.onDidChangeTextDocument((e) => {
+		const editor = vscode.window.activeTextEditor
+		if (!editor || editor.document !== e.document || !e.contentChanges.length) {
+			return
+		}
+
+		clearState()
+
+		// Check if this edit is from human typing rather than AI tools, copy-paste, etc.
+		// Only trigger autocomplete for human edits to avoid interference
+		const isHumanTyping = isHumanEdit(e)
+		if (!isHumanTyping) {
+			console.log("🚀🤖 Skipping autocomplete trigger during non-human edit")
+			return
+		}
+
+		// Reset the justAcceptedSuggestion flag when the user makes any edit
+		// This ensures we only skip one completion request after accepting a suggestion
+		justAcceptedSuggestion = false
+
+		// Detect backspace operations by checking content changes
+		const change = e.contentChanges[0]
+		if (change.rangeLength > 0 && change.text === "") {
+			isBackspaceOperation = true
+		}
+
+		// Force inlineSuggestions to appear, even for whitespace changes
+		// without this, hitting keys like spacebar won't show the completion
+		vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
+	})
+
+	// Create a composite disposable to return
+	const disposable = new vscode.Disposable(() => {
+		providerDisposable.dispose()
+		toggleCommand.dispose()
+		trackAcceptedSuggestionCommand.dispose()
+		statusBar.dispose()
+		selectionHandler.dispose()
+		documentHandler.dispose()
+		animationManager.dispose()
+	})
+
+	// Still register with context for safety
+	context.subscriptions.push(disposable)
+
+	// Initialize status bar with correct state
+	updateStatusBar()
+
+	return disposable
+}
+
+/**
+ * Creates an inline completion item with tracking command
+ * @param completionText The text to be inserted as completion
+ * @param insertRange The range where the completion should be inserted
+ * @param position The position in the document
+ * @returns A configured vscode.InlineCompletionItem
+ */
+function createInlineCompletionItem(completionText: string, position: vscode.Position): vscode.InlineCompletionItem {
+	const insertRange = new vscode.Range(position, position)
+
+	return Object.assign(new vscode.InlineCompletionItem(completionText, insertRange), {
+		command: {
+			command: "qax-code.trackAcceptedSuggestion",
+			title: "Track Accepted Suggestion",
+			arguments: [completionText, position],
+		},
+	})
+}
diff --git a/src/services/autocomplete/ContextGatherer.ts b/src/services/autocomplete/ContextGatherer.ts
new file mode 100644
index ********..4b31b312
--- /dev/null
+++ b/src/services/autocomplete/ContextGatherer.ts
@@ -0,0 +1,748 @@
+//PLANREF: continue/core/autocomplete/context/ContextRetrievalService.ts
+//PLANREF: continue/extensions/vscode/src/autocomplete/recentlyEdited.ts
+//PLANREF: continue/extensions/vscode/src/autocomplete/RecentlyVisitedRangesService.ts
+//PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts
+import * as vscode from "vscode"
+import { LRUCache } from "lru-cache"
+// AIDIFF: Removed unused URI import
+// import * as URI from "uri-js"
+// AIDIFF: Assuming tree-sitter utilities are available from this path.
+// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (for getAst, getTreePathAtCursor)
+import { getAst, getTreePathAtCursor } from "./utils/treeSitter"
+import type Parser from "web-tree-sitter"
+
+// AIDIFF: Define a simplified AutocompleteLanguageInfo if not available globally.
+// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts
+interface AutocompleteLanguageInfo {
+	singleLineComment?: string
+	// Add other properties if needed by adapted LSP logic
+}
+
+// AIDIFF: Define constants for node types, similar to continue/core/indexing/chunk/code.ts
+// PLANREF: continue/core/indexing/chunk/code.ts
+const FUNCTION_DECLARATION_NODE_TYPES = [
+	"arrow_function",
+	"function_declaration",
+	"function_definition", // C++, Python
+	"method_declaration",
+	"sub_declaration", // VB
+	// TODO: other languages
+]
+const FUNCTION_BLOCK_NODE_TYPES = [
+	"block", // JS, C++, Java
+	"statement_block", // C#
+	"compound_statement", // C, C++
+	"sub_body", // VB
+	// TODO: other languages
+]
+
+/**
+ * Interface for code context
+ */
+export interface CodeContextDefinition {
+	filepath: string
+	content: string
+	// AIDIFF: VSCode's Range is slightly different, ensure compatibility or convert.
+	// For now, sticking to our existing structure.
+	range: {
+		start: { line: number; character: number }
+		end: { line: number; character: number }
+	}
+	// AIDIFF: Optional field to denote the source of this context item
+	source?: "lsp" | "recent_edit" | "recent_visit" | "import"
+}
+export interface CodeContext {
+	currentLine: string
+	precedingLines: string[]
+	followingLines: string[]
+	imports: string[] // Keep this for simple import strings
+	definitions: CodeContextDefinition[] // This will now hold combined context
+}
+
+// PLANREF: continue/extensions/vscode/src/autocomplete/recentlyEdited.ts
+interface RecentlyEditedRangeInternal {
+	uri: vscode.Uri
+	range: vscode.Range
+	timestamp: number
+	content: string // AIDIFF: Store content directly for simplicity
+}
+
+// PLANREF: continue/extensions/vscode/src/autocomplete/RecentlyVisitedRangesService.ts
+interface RecentlyVisitedSnippetInternal extends CodeContextDefinition {
+	timestamp: number
+}
+
+/**
+ * Gathers relevant code context for autocomplete
+ */
+export class ContextGatherer {
+	private maxPrecedingLines: number
+	private maxFollowingLines: number
+	private maxImports: number
+	private maxDefinitionsToFetch: number // AIDIFF: Renamed for clarity, as definitions come from multiple sources
+
+	// PLANREF: continue/extensions/vscode/src/autocomplete/recentlyEdited.ts
+	private static readonly RECENTLY_EDITED_STALE_TIME_MS = 1000 * 60 * 2 // 2 minutes
+	private static readonly MAX_RECENTLY_EDITED_RANGES = 3
+	private recentlyEditedRanges: RecentlyEditedRangeInternal[] = []
+	private recentlyEditedDocuments: { uri: vscode.Uri; timestamp: number }[] = []
+	private static readonly MAX_RECENTLY_EDITED_DOCUMENTS = 10
+
+	// PLANREF: continue/extensions/vscode/src/autocomplete/RecentlyVisitedRangesService.ts
+	private recentlyVisitedCache: LRUCache<string, RecentlyVisitedSnippetInternal[]>
+	private static readonly RECENTLY_VISITED_NUM_SURROUNDING_LINES = 20
+	private static readonly MAX_RECENT_FILES_FOR_VISITED = 3
+	private static readonly MAX_SNIPPETS_PER_VISITED_FILE = 3
+
+	// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts
+	private lspGotoCache: LRUCache<string, vscode.Location[]> // AIDIFF: Using LRUCache for LSP results too
+	private static readonly MAX_LSP_CACHE_SIZE = 100 // AIDIFF: Reduced from 500 for potentially less memory usage
+
+	private disposables: vscode.Disposable[] = []
+
+	constructor(
+		maxPrecedingLines: number = 20,
+		maxFollowingLines: number = 10,
+		maxImports: number = 20,
+		maxDefinitionsToFetch: number = 5, // AIDIFF: This now applies primarily to LSP fetching per symbol
+	) {
+		this.maxPrecedingLines = maxPrecedingLines
+		this.maxFollowingLines = maxFollowingLines
+		this.maxImports = maxImports
+		this.maxDefinitionsToFetch = maxDefinitionsToFetch
+
+		// Initialize Recently Visited Cache
+		// PLANREF: continue/extensions/vscode/src/autocomplete/RecentlyVisitedRangesService.ts (constructor)
+		this.recentlyVisitedCache = new LRUCache<string, RecentlyVisitedSnippetInternal[]>({
+			max: ContextGatherer.MAX_RECENT_FILES_FOR_VISITED,
+		})
+
+		// Initialize LSP Cache
+		// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (gotoCache)
+		this.lspGotoCache = new LRUCache<string, vscode.Location[]>({
+			max: ContextGatherer.MAX_LSP_CACHE_SIZE,
+		})
+
+		// Setup listeners for recently edited and visited
+		// PLANREF: continue/extensions/vscode/src/autocomplete/recentlyEdited.ts (constructor)
+		this.disposables.push(vscode.workspace.onDidChangeTextDocument(this._handleTextDocumentChange.bind(this)))
+		// PLANREF: continue/extensions/vscode/src/autocomplete/RecentlyVisitedRangesService.ts (constructor, initWithPostHog)
+		this.disposables.push(vscode.window.onDidChangeTextEditorSelection(this._handleTextEditorSelectionChange.bind(this)))
+
+		// PLANREF: continue/extensions/vscode/src/autocomplete/recentlyEdited.ts (constructor interval)
+		const intervalId = setInterval(this._removeOldRecentlyEditedEntries.bind(this), 1000 * 15)
+		this.disposables.push({ dispose: () => clearInterval(intervalId) })
+	}
+
+	public dispose() {
+		this.disposables.forEach((d) => d.dispose())
+		this.disposables = []
+		this.recentlyVisitedCache.clear()
+		this.lspGotoCache.clear()
+	}
+
+	// AIDIFF: Helper to read file content, used by various new services
+	private async _readFileContent(uri: vscode.Uri): Promise<string> {
+		try {
+			const contentBytes = await vscode.workspace.fs.readFile(uri)
+			return new TextDecoder().decode(contentBytes)
+		} catch (e) {
+			console.warn(`[ContextGatherer] Error reading file ${uri.toString()}:`, e)
+			return ""
+		}
+	}
+
+	// AIDIFF: Helper to read range in file, used by LSP logic
+	// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (ide.readRangeInFile)
+	private async _readRangeInFile(uri: vscode.Uri, range: vscode.Range): Promise<string> {
+		try {
+			const fullContent = await this._readFileContent(uri)
+			const lines = fullContent.split("\n")
+			// AIDIFF: Ensure start and end lines are within bounds
+			const startLine = Math.max(0, range.start.line)
+			const endLine = Math.min(lines.length - 1, range.end.line)
+
+			if (startLine > endLine) return "" // Invalid range
+
+			if (startLine === endLine) {
+				return lines[startLine].substring(range.start.character, range.end.character)
+			}
+
+			const relevantLines: string[] = []
+			relevantLines.push(lines[startLine].substring(range.start.character))
+			for (let i = startLine + 1; i < endLine; i++) {
+				relevantLines.push(lines[i])
+			}
+			relevantLines.push(lines[endLine].substring(0, range.end.character))
+			return relevantLines.join("\n")
+		} catch (e) {
+			console.warn(`[ContextGatherer] Error reading range in file ${uri.toString()}:`, e)
+			return ""
+		}
+	}
+
+	// --- Recently Edited Logic ---
+	// PLANREF: continue/extensions/vscode/src/autocomplete/recentlyEdited.ts
+	private async _handleTextDocumentChange(event: vscode.TextDocumentChangeEvent): Promise<void> {
+		// AIDIFF: Logic adapted from RecentlyEditedTracker.constructor
+		if (event.document.uri.scheme !== "file") {
+			return
+		}
+
+		event.contentChanges.forEach(async (change) => {
+			const editedRange = new vscode.Range(
+				new vscode.Position(change.range.start.line, 0),
+				// AIDIFF: Ensure end line captures the full lines affected by the change
+				new vscode.Position(change.range.end.line + change.text.split("\n").length - 1, 0),
+			)
+			await this._insertRecentlyEditedRange(event.document.uri, editedRange, Date.now())
+		})
+		this._insertRecentlyEditedDocument(event.document.uri)
+	}
+
+	private async _insertRecentlyEditedRange(uri: vscode.Uri, range: vscode.Range, timestamp: number): Promise<void> {
+		// AIDIFF: Logic adapted from RecentlyEditedTracker.insertRange
+		// For simplicity, we won't merge overlapping ranges as in the original,
+		// but rather just add new ones and cap the list.
+		// More sophisticated merging can be added later if needed.
+		try {
+			const content = await this._readRangeInFile(uri, range)
+			if (!content.trim()) return // Don't add empty edits
+
+			const newEntry: RecentlyEditedRangeInternal = {
+				uri,
+				range,
+				timestamp,
+				content,
+			}
+
+			// Remove existing entry for the same file to avoid too many from one file
+			this.recentlyEditedRanges = this.recentlyEditedRanges.filter((r) => r.uri.toString() !== uri.toString())
+
+			this.recentlyEditedRanges.unshift(newEntry)
+			if (this.recentlyEditedRanges.length > ContextGatherer.MAX_RECENTLY_EDITED_RANGES) {
+				this.recentlyEditedRanges = this.recentlyEditedRanges.slice(0, ContextGatherer.MAX_RECENTLY_EDITED_RANGES)
+			}
+		} catch (e) {
+			console.warn(`[ContextGatherer] Error inserting recently edited range:`, e)
+		}
+	}
+
+	private _insertRecentlyEditedDocument(uri: vscode.Uri): void {
+		// AIDIFF: Logic adapted from RecentlyEditedTracker.insertDocument
+		if (uri.scheme !== "file") return
+
+		this.recentlyEditedDocuments = this.recentlyEditedDocuments.filter((doc) => doc.uri.toString() !== uri.toString()) // Remove if already exists to update timestamp
+		this.recentlyEditedDocuments.unshift({ uri, timestamp: Date.now() })
+		if (this.recentlyEditedDocuments.length > ContextGatherer.MAX_RECENTLY_EDITED_DOCUMENTS) {
+			this.recentlyEditedDocuments = this.recentlyEditedDocuments.slice(0, ContextGatherer.MAX_RECENTLY_EDITED_DOCUMENTS)
+		}
+	}
+
+	private _removeOldRecentlyEditedEntries(): void {
+		// AIDIFF: Logic adapted from RecentlyEditedTracker.removeOldEntries
+		const now = Date.now()
+		this.recentlyEditedRanges = this.recentlyEditedRanges.filter(
+			(entry) => now - entry.timestamp < ContextGatherer.RECENTLY_EDITED_STALE_TIME_MS,
+		)
+		this.recentlyEditedDocuments = this.recentlyEditedDocuments.filter(
+			(entry) => now - entry.timestamp < ContextGatherer.RECENTLY_EDITED_STALE_TIME_MS,
+		) // Also prune documents
+	}
+
+	private async getRecentlyEditedContext(currentFileUri: vscode.Uri): Promise<CodeContextDefinition[]> {
+		// AIDIFF: Logic adapted from RecentlyEditedTracker.getRecentlyEditedRanges
+		// We'll return content directly as CodeContextDefinition
+		return this.recentlyEditedRanges
+			.filter((r) => r.uri.toString() !== currentFileUri.toString()) // Exclude current file
+			.map((entry) => ({
+				filepath: entry.uri.toString(),
+				content: entry.content,
+				range: {
+					start: {
+						line: entry.range.start.line,
+						character: entry.range.start.character,
+					},
+					end: {
+						line: entry.range.end.line,
+						character: entry.range.end.character,
+					},
+				},
+				source: "recent_edit" as const,
+			}))
+	}
+
+	// --- Recently Visited Logic ---
+	// PLANREF: continue/extensions/vscode/src/autocomplete/RecentlyVisitedRangesService.ts
+	private async _handleTextEditorSelectionChange(event: vscode.TextEditorSelectionChangeEvent): Promise<void> {
+		// AIDIFF: Logic adapted from RecentlyVisitedRangesService.cacheCurrentSelectionContext
+		if (event.textEditor.document.uri.scheme !== "file") {
+			return
+		}
+		const filepath = event.textEditor.document.uri.toString()
+		const line = event.selections[0].active.line
+
+		const startLine = Math.max(0, line - ContextGatherer.RECENTLY_VISITED_NUM_SURROUNDING_LINES)
+		const endLine = Math.min(
+			event.textEditor.document.lineCount - 1,
+			line + ContextGatherer.RECENTLY_VISITED_NUM_SURROUNDING_LINES,
+		)
+
+		if (startLine >= endLine) return
+
+		try {
+			const fileContent = await this._readFileContent(event.textEditor.document.uri)
+			const lines = fileContent.split("\n")
+			const relevantLines = lines.slice(startLine, endLine + 1).join("\n")
+
+			if (!relevantLines.trim()) return
+
+			const snippet: RecentlyVisitedSnippetInternal = {
+				filepath,
+				content: relevantLines,
+				range: {
+					start: { line: startLine, character: 0 },
+					end: {
+						line: endLine,
+						character: lines[endLine]?.length ?? 0,
+					},
+				},
+				timestamp: Date.now(),
+				source: "recent_visit",
+			}
+
+			const existingSnippets = this.recentlyVisitedCache.get(filepath) || []
+			const newSnippets = [...existingSnippets, snippet]
+				// AIDIFF: Simple deduplication based on content and start line for now
+				.filter(
+					(s, index, self) =>
+						index === self.findIndex((t) => t.content === s.content && t.range.start.line === s.range.start.line),
+				)
+				.sort((a, b) => b.timestamp - a.timestamp)
+				.slice(0, ContextGatherer.MAX_SNIPPETS_PER_VISITED_FILE)
+
+			this.recentlyVisitedCache.set(filepath, newSnippets)
+		} catch (e) {
+			console.warn(`[ContextGatherer] Error caching recently visited range:`, e)
+		}
+	}
+
+	private getRecentlyVisitedContext(currentFileUri: vscode.Uri): CodeContextDefinition[] {
+		// AIDIFF: Logic adapted from RecentlyVisitedRangesService.getSnippets
+		let allSnippets: RecentlyVisitedSnippetInternal[] = []
+		for (const filepath of this.recentlyVisitedCache.keys()) {
+			if (filepath === currentFileUri.toString()) continue // Exclude current file
+
+			const snippets = this.recentlyVisitedCache.get(filepath) || []
+			allSnippets.push(...snippets)
+		}
+
+		return allSnippets
+			.sort((a, b) => b.timestamp - a.timestamp) // Sort by most recent globally
+			.slice(0, ContextGatherer.MAX_RECENT_FILES_FOR_VISITED * ContextGatherer.MAX_SNIPPETS_PER_VISITED_FILE) // Global cap
+			.map(({ timestamp: _timestamp, ...snippet }) => snippet) // Remove timestamp for final context, prefix with _ to satisfy linter
+	}
+
+	// --- LSP Definition Logic ---
+	// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts
+	private async _executeGotoProvider(
+		uri: vscode.Uri,
+		position: vscode.Position,
+		providerName:
+			| "vscode.executeDefinitionProvider"
+			| "vscode.executeTypeDefinitionProvider"
+			| "vscode.executeDeclarationProvider"
+			| "vscode.executeImplementationProvider",
+		// | "vscode.executeReferenceProvider" // AIDIFF: References might be too noisy for autocomplete context initially
+	): Promise<vscode.Location[]> {
+		const cacheKey = `${providerName}:${uri.toString()}:${position.line}:${position.character}`
+		const cached = this.lspGotoCache.get(cacheKey)
+		if (cached) {
+			return cached
+		}
+
+		try {
+			const definitions = (await vscode.commands.executeCommand(providerName, uri, position)) as
+				| vscode.Location[]
+				| vscode.LocationLink[]
+
+			const results: vscode.Location[] = []
+			if (definitions) {
+				for (const d of definitions) {
+					if ((d as vscode.LocationLink).targetUri) {
+						results.push(
+							new vscode.Location((d as vscode.LocationLink).targetUri, (d as vscode.LocationLink).targetRange),
+						)
+					} else if ((d as vscode.Location).uri) {
+						results.push(d as vscode.Location)
+					}
+				}
+			}
+
+			this.lspGotoCache.set(cacheKey, results)
+			return results
+		} catch (e) {
+			console.warn(`[ContextGatherer] Error executing ${providerName}:`, e)
+			return []
+		}
+	}
+
+	// AIDIFF: Simplified findChildren, predicate based.
+	// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (findChildren)
+	private _findSyntaxNodes(node: any, predicate: (n: any) => boolean): any[] {
+		const matchingNodes: any[] = []
+		const queue: any[] = [node]
+		while (queue.length > 0) {
+			const currentNode = queue.shift()!
+			if (predicate(currentNode)) {
+				matchingNodes.push(currentNode)
+			}
+			queue.push(...currentNode.children.filter((child: any): child is any => child !== null))
+		}
+		return matchingNodes
+	}
+
+	// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (crawlTypes)
+	private async _crawlTypesLsp(
+		initialRif: {
+			uri: vscode.Uri
+			range: vscode.Range
+			content: string
+		},
+		depth: number = 1, // AIDIFF: Default depth 1 to limit complexity/time
+		visitedUrisAndRanges: Set<string> = new Set(),
+	): Promise<CodeContextDefinition[]> {
+		const results: CodeContextDefinition[] = []
+		if (depth < 0) return results
+
+		const initialKey = `${initialRif.uri.toString()}:${initialRif.range.start.line}`
+		if (visitedUrisAndRanges.has(initialKey)) return results
+		visitedUrisAndRanges.add(initialKey)
+
+		const ast = await getAst(initialRif.uri.toString(), initialRif.content)
+		if (!ast) return results
+
+		// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (findTypeIdentifiers)
+		const typeIdentifierNodes = this._findSyntaxNodes(
+			ast.rootNode,
+			(childNode) =>
+				childNode.type === "type_identifier" ||
+				(childNode.parent?.type === "ERROR" && // Handle parsing errors gracefully
+					childNode.type === "identifier" &&
+					/^[A-Z]/.test(childNode.text)), // Heuristic: starts with uppercase
+		)
+
+		for (const node of typeIdentifierNodes) {
+			const nodeKey = `${initialRif.uri.toString()}:${node.startPosition.row}`
+			if (visitedUrisAndRanges.has(nodeKey)) continue
+
+			const definitions = await this._executeGotoProvider(
+				initialRif.uri,
+				new vscode.Position(node.startPosition.row, node.startPosition.column),
+				"vscode.executeDefinitionProvider",
+			)
+
+			for (const def of definitions.slice(0, this.maxDefinitionsToFetch)) {
+				// AIDIFF: Limit definitions per symbol
+				const defKey = `${def.uri.toString()}:${def.range.start.line}`
+				if (visitedUrisAndRanges.has(defKey)) continue
+
+				try {
+					const content = await this._readRangeInFile(def.uri, def.range)
+					if (content.trim()) {
+						const definitionEntry = {
+							filepath: def.uri.toString(),
+							content,
+							range: {
+								start: {
+									line: def.range.start.line,
+									character: def.range.start.character,
+								},
+								end: {
+									line: def.range.end.line,
+									character: def.range.end.character,
+								},
+							},
+							source: "lsp" as const,
+						}
+						results.push(definitionEntry)
+						visitedUrisAndRanges.add(defKey)
+
+						// Recurse for types within this definition
+						results.push(
+							...(await this._crawlTypesLsp(
+								{ uri: def.uri, range: def.range, content },
+								depth - 1,
+								visitedUrisAndRanges,
+							)),
+						)
+					}
+				} catch (e) {
+					console.warn(`[ContextGatherer] Error reading content for crawled type:`, e)
+				}
+			}
+		}
+		return results
+	}
+
+	// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (getDefinitionsForNode)
+	private async _getDefinitionsForNodeLsp(
+		documentUri: vscode.Uri,
+		node: any,
+		langInfo?: AutocompleteLanguageInfo, // AIDIFF: Make langInfo optional
+	): Promise<CodeContextDefinition[]> {
+		const definitions: CodeContextDefinition[] = []
+		const nodeText = node.text // For logging or simple context
+
+		switch (node.type) {
+			case "call_expression":
+			case "member_expression": // e.g., obj.method()
+			case "identifier": // Could be a function or variable
+				{
+					const lspDefs = await this._executeGotoProvider(
+						documentUri,
+						new vscode.Position(node.startPosition.row, node.startPosition.column),
+						"vscode.executeDefinitionProvider",
+					)
+
+					for (const lspDef of lspDefs.slice(0, this.maxDefinitionsToFetch)) {
+						try {
+							let content = await this._readRangeInFile(lspDef.uri, lspDef.range)
+							const originalContent = content
+							const maxLines = 15 // AIDIFF: Max lines for a definition snippet
+							if (content.split("\n").length > maxLines) {
+								// Try to get function signature if it's a long function
+								// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (function truncation logic)
+								const defAst = await getAst(lspDef.uri.toString(), content)
+								if (defAst) {
+									const funcNode = this._findSyntaxNodes(defAst.rootNode, (n) =>
+										FUNCTION_DECLARATION_NODE_TYPES.includes(n.type),
+									).find((fn) => fn.startPosition.row === 0) // Assuming defAst is for the range
+
+									if (funcNode) {
+										const bodyNode = funcNode.children.find(
+											(c: any) => c !== null && FUNCTION_BLOCK_NODE_TYPES.includes(c.type),
+										)
+										if (bodyNode) {
+											content = defAst.rootNode.text.substring(0, bodyNode.startIndex).trim()
+											if (langInfo?.singleLineComment) {
+												content += `\n${langInfo.singleLineComment} ...body omitted...`
+											} else {
+												content += `\n// ...body omitted...`
+											}
+										} else {
+											content = content.split("\n").slice(0, 1).join("\n") // Fallback to first line
+										}
+									} else {
+										content = content.split("\n").slice(0, 1).join("\n")
+									}
+								} else {
+									content = content.split("\n").slice(0, 1).join("\n")
+								}
+							}
+
+							if (content.trim()) {
+								const defEntry = {
+									filepath: lspDef.uri.toString(),
+									content,
+									range: {
+										start: {
+											line: lspDef.range.start.line,
+											character: lspDef.range.start.character,
+										},
+										end: {
+											line: lspDef.range.end.line,
+											character: lspDef.range.end.character,
+										},
+									},
+									source: "lsp" as const,
+								}
+								definitions.push(defEntry)
+								// Crawl types within this definition
+								definitions.push(
+									...(await this._crawlTypesLsp({
+										uri: lspDef.uri,
+										range: lspDef.range,
+										content: originalContent, // Crawl on original content
+									})),
+								)
+							}
+						} catch (e) {
+							console.warn(`[ContextGatherer] Error processing LSP definition for ${nodeText}:`, e)
+						}
+					}
+				}
+				break
+			// AIDIFF: Add more cases as needed, e.g., for new_expression, variable_declarator
+			// For now, focusing on call_expression and identifiers.
+		}
+		return definitions
+	}
+
+	// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (getDefinitionsFromLsp)
+	// AIDIFF: This replaces the old getDefinitions method.
+	private async _getLspDefinitions(document: vscode.TextDocument, position: vscode.Position): Promise<CodeContextDefinition[]> {
+		const allLspDefinitions: CodeContextDefinition[] = []
+		try {
+			const fileContent = document.getText()
+			const ast = await getAst(document.uri.toString(), fileContent)
+			if (!ast) return []
+
+			// AIDIFF: Convert vscode.Position to character offset for getTreePathAtCursor
+			const cursorIndex = document.offsetAt(position)
+			const treePath = await getTreePathAtCursor(ast, cursorIndex)
+			if (!treePath || treePath.length === 0) return []
+
+			// AIDIFF: Get language configuration for comments, if possible
+			let langInfo: AutocompleteLanguageInfo | undefined
+			// AIDIFF: Removed unused langConfig and langs variables and the try-catch block around it.
+			// The logic for getting language-specific comments was noted as incorrect and is omitted for now.
+			// try {
+			// 	// const langConfig = await vscode.languages.getLanguages().then((langs) => {
+			// 	// This is not how you get LanguageConfiguration.
+			// 	// For now, this part will be simplified or omitted.
+			// 	// A proper way would involve vscode.extensions.getExtension and its package.json contributions.
+			// 	// Or, a mapping of languageId to comment syntax.
+			// 	// });
+			// 	// langInfo = { singleLineComment: ... }
+			// } catch (e) {
+			// 	/* ignore */
+			// }
+
+			// Iterate over nodes in the tree path (from specific to general)
+			// PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts (loop over treePath.reverse())
+			for (const node of treePath.reverse()) {
+				if (allLspDefinitions.length >= this.maxDefinitionsToFetch * 3) break // Global cap for LSP defs
+
+				const nodeDefinitions = await this._getDefinitionsForNodeLsp(document.uri, node, langInfo)
+				for (const def of nodeDefinitions) {
+					// Deduplicate
+					if (
+						!allLspDefinitions.some(
+							(existing) =>
+								existing.filepath === def.filepath &&
+								existing.range.start.line === def.range.start.line &&
+								existing.content.length === def.content.length, // Simple content check
+						)
+					) {
+						allLspDefinitions.push(def)
+					}
+				}
+			}
+		} catch (e) {
+			console.warn(`[ContextGatherer] Error getting LSP definitions:`, e)
+		}
+		return allLspDefinitions.slice(0, this.maxDefinitionsToFetch * 5) // Final cap
+	}
+
+	/**
+	 * Gather context for autocomplete
+	 * @param document Current document
+	 * @param position Cursor position
+	 * @param useImports Whether to include imports (currently uses simple extraction)
+	 * @param useDefinitions Whether to include definitions (now uses LSP, recent edits, recent visits)
+	 * @returns Code context
+	 */
+	async gatherContext(
+		document: vscode.TextDocument,
+		position: vscode.Position,
+		useImports: boolean = true,
+		useDefinitions: boolean = true,
+	): Promise<CodeContext> {
+		// AIDIFF: Orchestration of different context sources
+		// PLANREF: Inspired by continue/core/autocomplete/context/ContextRetrievalService.ts general idea of combining sources
+
+		const content = document.getText()
+		const lines = content.split("\n")
+		const currentLine = lines[position.line] ?? ""
+
+		const precedingLines = lines
+			.slice(Math.max(0, position.line - this.maxPrecedingLines), position.line)
+			.filter((line) => line.trim().length > 0)
+
+		const followingLines = lines
+			.slice(position.line + 1, position.line + 1 + this.maxFollowingLines)
+			.filter((line) => line.trim().length > 0)
+
+		let importStrings: string[] = []
+		if (useImports) {
+			importStrings = await this.extractImports(document) // Keep existing simple import extraction
+		}
+
+		let allDefinitions: CodeContextDefinition[] = []
+		if (useDefinitions) {
+			// 1. LSP Definitions
+			// AIDIFF: Call the new LSP-based definition fetcher
+			// const lspDefinitions = await this._getLspDefinitions(document, position)
+			// allDefinitions.push(...lspDefinitions)
+
+			// // 2. Recently Edited Context
+			// // AIDIFF: Fetch and add recently edited context
+			// const recentlyEdited = await this.getRecentlyEditedContext(document.uri)
+			// allDefinitions.push(...recentlyEdited)
+
+			// 3. Recently Visited Context
+			// AIDIFF: Fetch and add recently visited context
+			// const recentlyVisited = this.getRecentlyVisitedContext(document.uri)
+			// allDefinitions.push(...recentlyVisited)
+
+			// AIDIFF: Deduplicate definitions from all sources
+			// Simple deduplication based on filepath and start line for now
+			const uniqueDefinitions = new Map<string, CodeContextDefinition>()
+			for (const def of allDefinitions) {
+				const key = `${def.filepath}:${def.range.start.line}`
+				if (!uniqueDefinitions.has(key)) {
+					uniqueDefinitions.set(key, def)
+				} else {
+					// Prioritize LSP if duplicate, then recent_edit, then recent_visit
+					const existing = uniqueDefinitions.get(key)!
+					if (def.source === "lsp" && existing.source !== "lsp") {
+						uniqueDefinitions.set(key, def)
+					} else if (def.source === "recent_edit" && existing.source === "recent_visit") {
+						uniqueDefinitions.set(key, def)
+					}
+				}
+			}
+			allDefinitions = Array.from(uniqueDefinitions.values())
+
+			// AIDIFF: Apply a global limit to the total number of definitions included
+			allDefinitions = allDefinitions.slice(0, this.maxDefinitionsToFetch * 10) // Generous global cap
+		}
+
+		return {
+			currentLine,
+			precedingLines,
+			followingLines,
+			imports: importStrings,
+			definitions: allDefinitions,
+		}
+	}
+
+	// Keep existing extractImports, or enhance it later if needed.
+	// For now, the task focuses on new context sources.
+	private async extractImports(document: vscode.TextDocument): Promise<string[]> {
+		const content = document.getText()
+		const lines = content.split("\n")
+		const imports: string[] = []
+
+		const importPatterns = [
+			/^\s*import\s+.*?from\s+['"].*?['"]/,
+			/^\s*import\s+['"].*?['"]/,
+			/^\s*const\s+.*?\s*=\s*require\(['"].*?['"]\)/,
+			/^\s*from\s+.*?import\s+.*/, // Python: from module import something
+			/^\s*import\s+.*/, // Python: import module
+			/^\s*using\s+.*;/,
+			/^\s*#include\s+[<"].*?[>"]/,
+		]
+
+		for (const line of lines) {
+			if (importPatterns.some((pattern) => pattern.test(line))) {
+				imports.push(line.trim())
+				if (imports.length >= this.maxImports) {
+					break
+				}
+			}
+		}
+		return imports
+	}
+}
diff --git a/src/services/autocomplete/__tests__/PromptRenderer.test.ts.ignore b/src/services/autocomplete/__tests__/PromptRenderer.test.ts.ignore
new file mode 100644
index ********..0c5a4a4a
--- /dev/null
+++ b/src/services/autocomplete/__tests__/PromptRenderer.test.ts.ignore
@@ -0,0 +1,273 @@
+import { PromptRenderer, type PromptOptions as _PromptOptions } from "../PromptRenderer"
+import { type CodeContext, type CodeContextDefinition as _CodeContextDefinition } from "../ContextGatherer"
+import { type AutocompleteLanguageInfo, getLanguageInfo } from "../AutocompleteLanguageInfo"
+import { type AutocompleteTemplate, getTemplateForModel } from "../templating/AutocompleteTemplate"
+import { getStopTokens } from "../templating/getStopTokens"
+import * as vscode from "vscode"
+
+// Mock dependencies
+jest.mock("../templating/AutocompleteTemplate", () => ({
+	getTemplateForModel: jest.fn(),
+}))
+jest.mock("../templating/getStopTokens", () => ({
+	getStopTokens: jest.fn(),
+}))
+jest.mock("../AutocompleteLanguageInfo", () => ({
+	getLanguageInfo: jest.fn(),
+}))
+
+// Mock vscode for activeTextEditor and workspaceFolders
+jest.mock("vscode", () => ({
+	window: {
+		activeTextEditor: undefined, // Default to undefined, can be set in tests
+	},
+	workspace: {
+		workspaceFolders: undefined, // Default to undefined
+	},
+	Uri: {
+		// Basic Uri mock for getUriPathBasename
+		parse: jest.fn((uriStr) => ({
+			fsPath: uriStr.startsWith("file://") ? uriStr.substring(7) : uriStr,
+			path: uriStr.startsWith("file://") ? uriStr.substring(7) : uriStr,
+			toString: () => uriStr,
+		})),
+		file: jest.fn((pathStr) => ({
+			fsPath: pathStr,
+			path: pathStr,
+			toString: () => `file://${pathStr}`,
+		})),
+	},
+}))
+
+describe("PromptRenderer", () => {
+	let promptRenderer: PromptRenderer
+	const mockDefaultModel = "test-model"
+
+	const mockPythonLangInfo: AutocompleteLanguageInfo = {
+		name: "Python",
+		singleLineComment: "#",
+		topLevelKeywords: ["def", "class"], // Example keywords
+	}
+
+	const mockTypeScriptLangInfo: AutocompleteLanguageInfo = {
+		name: "TypeScript",
+		singleLineComment: "//",
+		topLevelKeywords: ["function", "class"], // Example keywords
+	}
+
+	beforeEach(() => {
+		promptRenderer = new PromptRenderer({}, mockDefaultModel)
+		;(getLanguageInfo as jest.Mock).mockReturnValue(mockTypeScriptLangInfo) // Default mock
+		;(getStopTokens as jest.Mock).mockReturnValue(["\n\n", "function"])
+		;(vscode.window.activeTextEditor as any) = {
+			document: { uri: vscode.Uri.file("/test/project/file.ts") },
+		}
+		;(vscode.workspace.workspaceFolders as any) = [
+			{ uri: vscode.Uri.file("/test/project"), name: "project", index: 0 },
+		]
+	})
+
+	afterEach(() => {
+		jest.clearAllMocks()
+	})
+
+	const basicCodeContext: CodeContext = {
+		currentLine: "  const x = 1",
+		precedingLines: ["function greet() {", "  // A comment"],
+		followingLines: ["  return x;", "}"],
+		imports: [],
+		definitions: [],
+	}
+
+	const codeContextWithImports: CodeContext = {
+		...basicCodeContext,
+		imports: ['import fs from "fs";', 'import path from "path";'],
+	}
+
+	const codeContextWithDefinitions: CodeContext = {
+		...basicCodeContext,
+		definitions: [
+			{
+				filepath: "file:///utils/math.ts",
+				content: "export function add(a, b) {\n  return a + b;\n}",
+				range: { start: { line: 0, character: 0 }, end: { line: 2, character: 1 } },
+				source: "lsp",
+			},
+			{
+				filepath: "file:///helpers/string.ts",
+				content: 'export const GREETING = "Hello";',
+				range: { start: { line: 0, character: 0 }, end: { line: 0, character: 30 } },
+				source: "recent_edit",
+			},
+		],
+	}
+
+	it("should render a simple prompt using a string template", () => {
+		const mockTemplate: AutocompleteTemplate = {
+			template:
+				"Prefix: {{{prefix}}} Suffix: {{{suffix}}} File: {{{filename}}} Repo: {{{reponame}}} Lang: {{{language}}}",
+			completionOptions: { stop: ["stop1"] },
+		}
+		;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplate)
+
+		const result = promptRenderer.renderPrompt(basicCodeContext, []) // Added empty array for snippets
+
+		expect(getTemplateForModel).toHaveBeenCalledWith(mockDefaultModel)
+		expect(result.prompt).toBe(
+			"Prefix: function greet() {\n  // A comment\n  const x = 1 Suffix: \n  return x;\n} File: file.ts Repo: project Lang: TypeScript",
+		)
+		expect(result.prefix).toBe("function greet() {\n  // A comment\n  const x = 1")
+		expect(result.suffix).toBe("\n  return x;\n}")
+		expect(result.completionOptions).toEqual(
+			expect.objectContaining({
+				stop: ["\n\n", "function"], // From the global getStopTokens mock
+			}),
+		)
+	})
+
+	it("should render a prompt using a function template", () => {
+		const mockTemplateFn = jest.fn(
+			(prefix, suffix, filename, reponame, lang, snippets, workspaceUris) =>
+				`FUNC_TPL: ${prefix} | ${suffix} | ${filename} | ${reponame} | ${lang} | Snippets: ${snippets.length} | WS: ${workspaceUris.length}`,
+		)
+		const mockTemplate: AutocompleteTemplate = {
+			template: mockTemplateFn,
+			completionOptions: {},
+		}
+		;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplate)
+
+		promptRenderer.renderPrompt(basicCodeContext, []) // Added empty array for snippets
+		expect(mockTemplateFn).toHaveBeenCalledWith(
+			"function greet() {\n  // A comment\n  const x = 1", // prefix
+			"\n  return x;\n}", // suffix
+			"/test/project/file.ts", // filepath
+			"project", // reponame
+			"TypeScript", // langName
+			[], // snippets
+			["file:///test/project"], // workspaceUris
+		)
+	})
+
+	it("should include imports when enabled and context has imports (string template)", () => {
+		const mockTemplate: AutocompleteTemplate = {
+			template: "{{{prefix}}}", // Only care about prefix for this test
+			completionOptions: {},
+		}
+		;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplate)
+		;(getLanguageInfo as jest.Mock).mockReturnValue(mockPythonLangInfo)
+
+		const result = promptRenderer.renderPrompt(codeContextWithImports, [], {
+			includeImports: true,
+			language: "python",
+		}) // Added empty array for snippets
+		expect(result.prefix).toContain('// Path: file.ts\nimport fs from "fs";') // Assuming default filename from setup
+		expect(result.prefix).toContain('// Path: file.ts\nimport path from "path";')
+		expect(result.prefix).toContain("function greet() {\n  // A comment\n  const x = 1")
+	})
+
+	it("should include definitions when enabled and context has definitions (string template)", () => {
+		const mockTemplate: AutocompleteTemplate = {
+			template: "{{{prefix}}}",
+			completionOptions: {},
+		}
+		;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplate)
+
+		const result = promptRenderer.renderPrompt(codeContextWithDefinitions, [], { includeDefinitions: true }) // Added empty array for snippets
+		expect(result.prefix).toContain("// Path: math.ts\nexport function add(a, b) {\n  return a + b;\n}")
+		expect(result.prefix).toContain('// Path: string.ts\nexport const GREETING = "Hello";')
+		expect(result.prefix).toContain("function greet() {\n  // A comment\n  const x = 1")
+	})
+
+	it("should use compilePrefixSuffix if template provides it", () => {
+		const mockCompileFn = jest.fn(
+			(prefix, suffix, _filepath, _reponame, _snippets, _workspaceUris) =>
+				[`COMPILED: ${prefix}`, `COMPILED_SUFFIX: ${suffix}`] as [string, string],
+		)
+		const mockTemplate: AutocompleteTemplate = {
+			template: "{{{prefix}}}{{{suffix}}}",
+			compilePrefixSuffix: mockCompileFn,
+			completionOptions: {},
+		}
+		;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplate)
+
+		const result = promptRenderer.renderPrompt(basicCodeContext, []) // Added empty array for snippets
+		expect(mockCompileFn).toHaveBeenCalled()
+		expect(result.prefix).toBe("COMPILED: function greet() {\n  // A comment\n  const x = 1")
+		expect(result.suffix).toBe("COMPILED_SUFFIX: \n  return x;\n}")
+		expect(result.prompt).toBe(
+			"COMPILED: function greet() {\n  // A comment\n  const x = 1COMPILED_SUFFIX: \n  return x;\n}",
+		)
+	})
+
+	it("should handle empty suffix correctly by defaulting to newline", () => {
+		const contextWithoutFollowing: CodeContext = { ...basicCodeContext, followingLines: [] }
+		const mockTemplate: AutocompleteTemplate = { template: "{{{prefix}}}---{{{suffix}}}", completionOptions: {} }
+		;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplate)
+
+		const result = promptRenderer.renderPrompt(contextWithoutFollowing, []) // Added empty array for snippets
+		expect(result.suffix).toBe("\n")
+		expect(result.prompt).toContain("---") // Ensure suffix was actually part of template
+		expect(result.prompt.endsWith("---\n")).toBe(true)
+	})
+
+	it("should use default reponame and filename if not available from vscode", () => {
+		;(vscode.window.activeTextEditor as any) = undefined
+		;(vscode.workspace.workspaceFolders as any) = undefined
+		const mockTemplate: AutocompleteTemplate = {
+			template: "File: {{{filename}}} Repo: {{{reponame}}}",
+			completionOptions: {},
+		}
+		;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplate)
+
+		const result = promptRenderer.renderPrompt(basicCodeContext, []) // Added empty array for snippets
+		expect(result.prompt).toBe("File: untitled.txt Repo: my-repository")
+	})
+
+	describe("getStopTokens", () => {
+		it("should call the utility function with correct parameters", () => {
+			const mockTemplateForStop: AutocompleteTemplate = { template: "", completionOptions: { temperature: 0.5 } }
+			;(getTemplateForModel as jest.Mock).mockReturnValue(mockTemplateForStop)
+			;(getLanguageInfo as jest.Mock).mockReturnValue(mockPythonLangInfo)
+			;(getStopTokens as jest.Mock).mockReturnValue(["CUSTOM_STOP"])
+
+			const rendererWithOptions = new PromptRenderer({ language: "python" }, "custom-py-model")
+			const stopTokens = rendererWithOptions.getStopTokens()
+
+			expect(getTemplateForModel).toHaveBeenCalledWith("custom-py-model")
+			expect(getLanguageInfo).toHaveBeenCalledWith("python")
+			expect(getStopTokens).toHaveBeenCalledWith(
+				mockTemplateForStop.completionOptions,
+				mockPythonLangInfo,
+				"custom-py-model",
+			)
+			expect(stopTokens).toEqual(["CUSTOM_STOP"])
+		})
+	})
+
+	describe("extractCompletion", () => {
+		it("should return trimmed response if no markdown", () => {
+			const response = "  const x = 10;  "
+			expect(promptRenderer.extractCompletion(response)).toBe("const x = 10;")
+		})
+
+		it("should extract content from markdown code block", () => {
+			const response = '```typescript\nconst greeting = "hello";\nconsole.log(greeting);\n```'
+			expect(promptRenderer.extractCompletion(response)).toBe('const greeting = "hello";\nconsole.log(greeting);')
+		})
+
+		it("should extract content from markdown code block without language", () => {
+			const response = "```\nlet num = 42;\nnum++;\n```"
+			expect(promptRenderer.extractCompletion(response)).toBe("let num = 42;\nnum++;")
+		})
+
+		it("should handle empty content in markdown block", () => {
+			const response = "```javascript\n\n```"
+			expect(promptRenderer.extractCompletion(response)).toBe("")
+		})
+
+		it("should trim content inside code block", () => {
+			const response = '```\n  \n  let message = "  Hi  ";  \n  \n```'
+			expect(promptRenderer.extractCompletion(response)).toBe('let message = "  Hi  ";')
+		})
+	})
+})
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/__init__.py b/src/services/autocomplete/__tests__/fixtures/contextGatherer/__init__.py
new file mode 100644
index ********..8b137891
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/__init__.py
@@ -0,0 +1 @@
+
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/base_module.py b/src/services/autocomplete/__tests__/fixtures/contextGatherer/base_module.py
new file mode 100644
index ********..2edbd37b
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/base_module.py
@@ -0,0 +1,26 @@
+# File: base_module.py
+
+class BaseClass:
+    def __init__(self):
+        print("BaseClass initialized")
+
+class Collection:
+    def __init__(self):
+        print("Collection initialized")
+
+class Address:
+    def __init__(self, street: str, city: str, zip_code: str):
+        self.street = street
+        self.city = city
+        self.zip_code = zip_code
+
+    def __str__(self):
+        return f"{self.street}, {self.city}, {self.zip_code}"
+
+class Person:
+    def __init__(self, name: str, address: Address):
+        self.name = name
+        self.address = address
+
+    def __str__(self):
+        return f"{self.name} lives at {self.address}"
\ No newline at end of file
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/file1.go b/src/services/autocomplete/__tests__/fixtures/contextGatherer/file1.go
new file mode 100644
index ********..94eec171
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/file1.go
@@ -0,0 +1,9 @@
+package main
+
+import (
+	"core/autocomplete/context/root-path-context/test/files/models"
+)
+
+func getAddress(user *models.User) *models.Address {
+	return user.Address
+}
\ No newline at end of file
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/file1.php b/src/services/autocomplete/__tests__/fixtures/contextGatherer/file1.php
new file mode 100644
index ********..a72f805e
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/file1.php
@@ -0,0 +1,32 @@
+<?php
+
+namespace BaseNamespace;
+
+use BaseNamespace\BaseClass;
+use BaseNamespace\Interfaces\FirstInterface;
+use BaseNamespace\Interfaces\SecondInterface;
+use BaseNamespace\Person;
+use BaseNamespace\Address;
+
+function getAddress(Person $person): Address
+{
+    return $person->getAddress();
+}
+
+class Group extends BaseClass implements FirstInterface, SecondInterface
+{
+    private array $people;
+
+    public function __construct(array $people)
+    {
+        parent::__construct();
+        $this->people = $people;
+    }
+
+    public function getPersonAddress(Person $person): Address
+    {
+        return getAddress($person);
+    }
+}
+
+?>
\ No newline at end of file
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/python/classes.py b/src/services/autocomplete/__tests__/fixtures/contextGatherer/python/classes.py
new file mode 100644
index ********..d752d843
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/python/classes.py
@@ -0,0 +1,11 @@
+class Group(BaseClass, Person):
+    pass
+
+class Group(metaclass=MetaGroup):
+    pass
+
+class Group(BaseClass[Address], Gathering[Person]):
+    pass
+
+class Group(List[Address], Person[str]):
+    pass
\ No newline at end of file
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/python/functions.py b/src/services/autocomplete/__tests__/fixtures/contextGatherer/python/functions.py
new file mode 100644
index ********..391d192c
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/python/functions.py
@@ -0,0 +1,41 @@
+from typing import List, Union, TypeVar, Generic
+
+T = TypeVar('T')
+
+class Address:
+    pass
+
+class Person:
+    pass
+
+class PersonWithGeneric(Generic[T]):
+    pass
+
+
+def get_address(person: Person) -> Address:
+    pass
+
+def get_group_address(people: Group[Person]) -> Group[Address]:
+    pass
+
+def log_person(person: Person) -> None:
+    pass
+
+def get_hardcoded_address() -> Address:
+    pass
+
+def log_person_or_address(value: Union[Person, Address]) -> Union[Person, Address]:
+    pass
+
+def log_person_and_address(person: Person, address: Address) -> None:
+    pass
+
+def get_address_generator(person: Person) -> Generator[Address, None, None]:
+    yield
+
+class Group:
+    def log_person_and_address(self, person: Person, address: Address) -> None:
+        pass
+
+async def get_person(address: Address) -> Person:
+    pass
\ No newline at end of file
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/arrowFunctions.ts b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/arrowFunctions.ts
new file mode 100644
index ********..031d4d8a
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/arrowFunctions.ts
@@ -0,0 +1,29 @@
+// @ts-nocheck
+
+const _getAddress = (_person: Person): Address => {
+	// TODO
+}
+
+const _logPerson = (_person: Person) => {
+	// TODO
+}
+
+const _getHardcodedAddress = (): Address => {
+	// TODO
+}
+
+const _getAddresses = (_people: Person[]): Address[] => {
+	// TODO
+}
+
+const _logPersonWithAddres = (_person: Person<Address>): Person<Address> => {
+	// TODO
+}
+
+const _logPersonOrAddress = (_person: Person | Address): Person | Address => {
+	// TODO
+}
+
+const _logPersonAndAddress = (_person: Person, _address: Address) => {
+	// TODO
+}
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/classMethods.ts b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/classMethods.ts
new file mode 100644
index ********..7bca91ea
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/classMethods.ts
@@ -0,0 +1,35 @@
+// @ts-nocheck
+
+class _Group {
+	_getPersonAddress(_person: Person): Address {
+		// TODO
+	}
+
+	_getHardcodedAddress(): Address {
+		// TODO
+	}
+
+	_addPerson(_person: Person) {
+		// TODO
+	}
+
+	_addPeople(_people: Person[]) {
+		// TODO
+	}
+
+	_getAddresses(_people: Person[]): Address[] {
+		// TODO
+	}
+
+	_logPersonWithAddress(_person: Person<Address>): Person<Address> {
+		// TODO
+	}
+
+	_logPersonOrAddress(_person: Person | Address): Person | Address {
+		// TODO
+	}
+
+	_logPersonAndAddress(_person: Person, _address: Address) {
+		// TODO
+	}
+}
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/classes.ts b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/classes.ts
new file mode 100644
index ********..c8bdc4b1
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/classes.ts
@@ -0,0 +1,9 @@
+// @ts-nocheck
+
+class _Group extends BaseClass {}
+
+class _Group2 implements FirstInterface {} // Renamed to avoid duplicate identifier
+
+class _Group3 extends BaseClass implements FirstInterface, SecondInterface {} // Renamed
+
+class _Group4 extends BaseClass<User> implements FirstInterface<User> {} // Renamed
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/functions.ts b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/functions.ts
new file mode 100644
index ********..ddda72cb
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/functions.ts
@@ -0,0 +1,33 @@
+// @ts-nocheck
+
+function _getAddress(_person: Person): Address {
+	// TODO
+}
+
+function _getFirstAddress(_people: Person[]): Address {
+	// TODO
+}
+
+function _logPerson(_person: Person) {
+	// TODO
+}
+
+function _getHardcodedAddress(): Address {
+	// TODO
+}
+
+function _getAddresses(_people: Person[]): Address[] {
+	// TODO
+}
+
+function _logPersonWithAddress(_person: Person<Address>): Person<Address> {
+	// TODO
+}
+
+function _logPersonOrAddress(_person: Person | Address): Person | Address {
+	// TODO
+}
+
+function _logPersonAndAddress(_person: Person, _address: Address) {
+	// TODO
+}
diff --git a/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/generators.ts b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/generators.ts
new file mode 100644
index ********..d4f2772c
--- /dev/null
+++ b/src/services/autocomplete/__tests__/fixtures/contextGatherer/typescript/generators.ts
@@ -0,0 +1,33 @@
+// @ts-nocheck
+
+function* _getAddress(_person: Person): Address {
+	// TODO
+}
+
+function* _getFirstAddress(_people: Person[]): Address {
+	// TODO
+}
+
+function* _logPerson(_person: Person) {
+	// TODO
+}
+
+function* _getHardcodedAddress(): Address {
+	// TODO
+}
+
+function* _getAddresses(_people: Person[]): Address[] {
+	// TODO
+}
+
+function* _logPersonWithAddress(_person: Person<Address>): Person<Address> {
+	// TODO
+}
+
+function* _logPersonOrAddress(_person: Person | Address): Person | Address {
+	// TODO
+}
+
+function* _logPersonAndAddress(_person: Person, _address: Address) {
+	// TODO
+}
diff --git a/src/services/autocomplete/context/ContextRetrievalService.ts b/src/services/autocomplete/context/ContextRetrievalService.ts
new file mode 100644
index ********..edbea6e0
--- /dev/null
+++ b/src/services/autocomplete/context/ContextRetrievalService.ts
@@ -0,0 +1,12 @@
+//PLANREF: continue/core/autocomplete/context/ContextRetrievalService.ts
+import { IDE } from "../utils/ide"
+
+import { ImportDefinitionsService } from "./ImportDefinitionsService"
+
+export class ContextRetrievalService {
+	private importDefinitionsService: ImportDefinitionsService
+
+	constructor(private readonly ide: IDE) {
+		this.importDefinitionsService = new ImportDefinitionsService(this.ide)
+	}
+}
diff --git a/src/services/autocomplete/context/ImportDefinitionsService.ts b/src/services/autocomplete/context/ImportDefinitionsService.ts
new file mode 100644
index ********..d9f75d27
--- /dev/null
+++ b/src/services/autocomplete/context/ImportDefinitionsService.ts
@@ -0,0 +1,99 @@
+//PLANREF: continue/extensions/vscode/src/autocomplete/lsp.ts
+import { RangeInFileWithContents } from "../ide-types"
+import { findUriInDirs } from "../templating/uri"
+import { IDE } from "../utils/ide"
+import { PrecalculatedLruCache } from "../utils/LruCache"
+import { getParserForFile, getFullLanguageName, getQueryForFile } from "../utils/treeSitter"
+
+interface FileInfo {
+	imports: { [key: string]: RangeInFileWithContents[] }
+}
+
+export class ImportDefinitionsService {
+	static N = 10
+
+	private cache: PrecalculatedLruCache<FileInfo> = new PrecalculatedLruCache<FileInfo>(
+		this._getFileInfo.bind(this),
+		ImportDefinitionsService.N,
+	)
+
+	constructor(private readonly ide: IDE) {
+		ide.onDidChangeActiveTextEditor((filepath) => {
+			this.cache.initKey(filepath).catch((e) => console.warn(`Failed to initialize ImportDefinitionService: ${e.message}`))
+		})
+	}
+
+	get(filepath: string): FileInfo | undefined {
+		return this.cache.get(filepath)
+	}
+
+	private async _getFileInfo(filepath: string): Promise<FileInfo | null> {
+		if (filepath.endsWith(".ipynb")) {
+			// Commenting out this line was the solution to https://github.com/continuedev/continue/issues/1463
+			return null
+		}
+
+		const parser = await getParserForFile(filepath)
+		if (!parser) {
+			return {
+				imports: {},
+			}
+		}
+
+		let fileContents: string | undefined = undefined
+		try {
+			const { foundInDir } = findUriInDirs(filepath, await this.ide.getWorkspaceDirs())
+			if (!foundInDir) {
+				return null
+			} else {
+				fileContents = await this.ide.readFile(filepath)
+			}
+		} catch (err) {
+			// File removed
+			return null
+		}
+
+		const ast = parser.parse(fileContents, undefined, {
+			includedRanges: [
+				{
+					startIndex: 0,
+					endIndex: 10_000,
+					startPosition: { row: 0, column: 0 },
+					endPosition: { row: 100, column: 0 },
+				},
+			],
+		})
+		const language = getFullLanguageName(filepath)
+		const query = await getQueryForFile(filepath, `import-queries/${language}.scm`)
+		if (!query) {
+			return {
+				imports: {},
+			}
+		}
+
+		// Using non-optional matches since query is already checked for null above
+		const matches = query.matches(ast.rootNode)
+
+		const fileInfo: FileInfo = {
+			imports: {},
+		}
+		for (const match of matches) {
+			const startPosition = match.captures[0].node.startPosition
+			const defs = await this.ide.gotoDefinition({
+				filepath: filepath,
+				position: {
+					line: startPosition.row,
+					character: startPosition.column,
+				},
+			})
+			fileInfo.imports[match.captures[0].node.text] = await Promise.all(
+				defs.map(async (def) => ({
+					...def,
+					contents: await this.ide.readRangeInFile(def.filepath, def.range),
+				})),
+			)
+		}
+
+		return fileInfo
+	}
+}
diff --git a/src/services/autocomplete/context/snippetProvider.ts b/src/services/autocomplete/context/snippetProvider.ts
new file mode 100644
index ********..7ace6118
--- /dev/null
+++ b/src/services/autocomplete/context/snippetProvider.ts
@@ -0,0 +1,29 @@
+import {
+	AutocompleteSnippetType,
+	type AutocompleteContextSnippet,
+	type AutocompleteCodeSnippet,
+} from "../templating/snippetTypes"
+import { CodeContextDefinition } from "../ContextGatherer"
+import { getUriPathBasename } from "../templating/uri"
+
+export const generateImportSnippets = (
+	includeImports: boolean,
+	imports: string[],
+	currentFilepath: string,
+): AutocompleteContextSnippet[] =>
+	(includeImports ? imports : []).map((importStatement, index) => ({
+		type: AutocompleteSnippetType.Context,
+		content: importStatement,
+		filepath: `context://imports/${getUriPathBasename(currentFilepath)}#${index}`,
+	}))
+
+export const generateDefinitionSnippets = (
+	includeDefinitions: boolean,
+	definitions: CodeContextDefinition[],
+): AutocompleteCodeSnippet[] =>
+	(includeDefinitions ? definitions : []).map((def) => ({
+		type: AutocompleteSnippetType.Code,
+		filepath: def.filepath,
+		content: def.content,
+		// language: def.language // Language is not on CodeContextDefinition, derived from main file or filepath extension if needed by template
+	}))
diff --git a/src/services/autocomplete/ide-types.d.ts b/src/services/autocomplete/ide-types.d.ts
new file mode 100644
index ********..5250c5d4
--- /dev/null
+++ b/src/services/autocomplete/ide-types.d.ts
@@ -0,0 +1,63 @@
+//PLANREF: continue/core/autocomplete/types.ts
+//PLANREF: continue/core/index.d.ts
+export interface Location {
+	filepath: string
+	position: Position
+}
+
+export interface FileWithContents {
+	filepath: string
+	contents: string
+}
+
+export interface Range {
+	start: Position
+	end: Position
+}
+
+export interface Position {
+	line: number
+	character: number
+}
+
+export interface FileEdit {
+	filepath: string
+	range: Range
+	replacement: string
+}
+
+export interface RangeInFile {
+	filepath: string
+	range: Range
+}
+
+export interface FileWithContents {
+	filepath: string
+	contents: string
+}
+
+export interface RangeInFileWithContents {
+	filepath: string
+	range: {
+		start: { line: number; character: number }
+		end: { line: number; character: number }
+	}
+	contents: string
+}
+export interface SymbolWithRange extends RangeInFile {
+	name: string
+	type: Parser.Node["type"]
+	content: string
+}
+
+export type FileSymbolMap = Record<string, SymbolWithRange[]>
+
+// AIDIFF: Added IdeInfo interface to support getIdeInfo() method in the IDE abstraction layer.
+// PLANREF: continue/core/index.d.ts (IdeInfo)
+export interface IdeInfo {
+	ideType: string // e.g., "vscode", "jetbrains"
+	name: string // e.g., "Visual Studio Code"
+	version: string // e.g., "1.85.1"
+	remoteName?: string // e.g., "ssh-remote", undefined for local
+	extensionVersion: string // Version of this extension
+}
diff --git a/src/services/autocomplete/templating/AutocompleteTemplate.ts b/src/services/autocomplete/templating/AutocompleteTemplate.ts
new file mode 100644
index ********..b3987682
--- /dev/null
+++ b/src/services/autocomplete/templating/AutocompleteTemplate.ts
@@ -0,0 +1,173 @@
+// AIDIFF: Updated to align with continue/core/autocomplete/templating/AutocompleteTemplate.ts
+// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts
+// Fill in the middle prompts
+
+import * as vscode from "vscode"
+import { CompletionOptions } from "../types"
+import { AutocompleteSnippet, AutocompleteSnippetType } from "./snippetTypes"
+import { CodeContext, CodeContextDefinition } from "../ContextGatherer"
+
+// AIDIFF: Updated interface to match continue/
+export interface AutocompleteTemplate {
+	compilePrefixSuffix?: (prefix: string, suffix: string) => [string, string]
+	getSystemPrompt: () => string
+	template: (
+		codeContext: CodeContext,
+		document: vscode.TextDocument,
+		position: vscode.Position,
+		snippets: AutocompleteSnippet[],
+	) => string
+	completionOptions?: Partial<CompletionOptions>
+}
+
+// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (gptAutocompleteTemplate)
+// const gptAutocompleteTemplate: AutocompleteTemplate = {
+// 	template: `\`\`\`
+// {{{prefix}}}[BLANK]{{{suffix}}}
+// \`\`\`
+//
+// Fill in the blank to complete the code block. Your response should include only the code to replace [BLANK], without surrounding backticks.`,
+// 	completionOptions: { stop: ["\n"] },
+// }
+
+// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (holeFillerTemplate)
+export const holeFillerTemplate: AutocompleteTemplate = {
+	getSystemPrompt: () => {
+		// From https://github.com/VictorTaelin/AI-scripts
+		const SYSTEM_MSG = `You are a HOLE FILLER. You are provided with a file containing holes, formatted as '{{HOLE_NAME}}'.
+		Your TASK is to complete with a string to replace this hole with, inside a <COMPLETION/> XML tag, including context-aware indentation, if needed.
+		All completions MUST be truthful, accurate, well-written and correct.
+## EXAMPLE QUERY:
+
+<QUERY>
+function sum_evens(lim) {
+  var sum = 0;
+  for (var i = 0; i < lim; ++i) {
+    {{FILL_HERE}}
+  }
+  return sum;
+}
+</QUERY>
+
+TASK: Fill the {{FILL_HERE}} hole.
+
+## CORRECT COMPLETION
+
+<COMPLETION>if (i % 2 === 0) {
+      sum += i;
+    }</COMPLETION>
+
+## EXAMPLE QUERY:
+
+<QUERY>
+def sum_list(lst):
+  total = 0
+  for x in lst:
+  {{FILL_HERE}}
+  return total
+
+print sum_list([1, 2, 3])
+</QUERY>
+
+## CORRECT COMPLETION:
+
+<COMPLETION>  total += x</COMPLETION>
+
+## EXAMPLE QUERY:
+
+<QUERY>
+// data Tree a = Node (Tree a) (Tree a) | Leaf a
+
+// sum :: Tree Int -> Int
+// sum (Node lft rgt) = sum lft + sum rgt
+// sum (Leaf val)     = val
+
+// convert to TypeScript:
+{{FILL_HERE}}
+</QUERY>
+
+## CORRECT COMPLETION:
+
+<COMPLETION>type Tree<T>
+  = {$:"Node", lft: Tree<T>, rgt: Tree<T>}
+  | {$:"Leaf", val: T};
+
+function sum(tree: Tree<number>): number {
+  switch (tree.$) {
+    case "Node":
+      return sum(tree.lft) + sum(tree.rgt);
+    case "Leaf":
+      return tree.val;
+  }
+}</COMPLETION>
+
+## EXAMPLE QUERY:
+
+The 5th {{FILL_HERE}} is Jupiter.
+
+## CORRECT COMPLETION:
+
+<COMPLETION>planet from the Sun</COMPLETION>
+
+## EXAMPLE QUERY:
+
+function hypothenuse(a, b) {
+  return Math.sqrt({{FILL_HERE}}b ** 2);
+}
+
+## CORRECT COMPLETION:
+
+<COMPLETION>a ** 2 + </COMPLETION>
+`
+		return SYSTEM_MSG
+	},
+	template: (
+		codeContext: CodeContext,
+		document: vscode.TextDocument,
+		position: vscode.Position,
+		snippets: AutocompleteSnippet[],
+	) => {
+		const offset = document.offsetAt(position)
+		const fileContent = document.getText()
+		const currentFileWithFillPlaceholder = fileContent.slice(0, offset) + "{{FILL_HERE}}" + fileContent.slice(offset)
+
+		const queryContextStrings: string[] = []
+
+		const codeContextItems = codeContext.definitions
+		if (codeContextItems && codeContextItems.length > 0) {
+			// AIDIFF: Ensure item.name is correct, CodeContextDefinition has filepath
+			const contextItemStrings = codeContextItems.map(
+				(item: CodeContextDefinition) => `// File: ${item.filepath}\n${item.content}`,
+			)
+			queryContextStrings.push(`// Context from other parts of the project:\n${contextItemStrings.join("\n\n")}`)
+		}
+
+		if (snippets && snippets.length > 0) {
+			const snippetStrings = snippets.map((snippet) => {
+				let header = `// Some context: ${snippet.type})`
+				if ("filepath" in snippet && (snippet as any).filepath) {
+					header = `// Some context: ${snippet.type}) from: ${(snippet as any).filepath}`
+				} else if (
+					snippet.type === AutocompleteSnippetType.Clipboard &&
+					"copiedAt" in snippet &&
+					(snippet as any).copiedAt
+				) {
+					header = `// Some context: ${snippet.type}, copiedAt: ${(snippet as any).copiedAt})`
+				}
+				return `${header}\n${snippet.content}`
+			})
+			queryContextStrings.push(`// Relevant snippets:\n${snippetStrings.join("\n\n")}`)
+		}
+
+		// Add the current file with hole last, as it's the primary focus
+		queryContextStrings.push(`// Current file content with hole:\n${currentFileWithFillPlaceholder}`)
+
+		const queryContent = queryContextStrings.join("\n\n")
+
+		const userPrompt = `\n\n<QUERY>\n${queryContent}\n</QUERY>\nTASK: Fill the {{FILL_HERE}} hole. Answer only with the CORRECT completion, and NOTHING ELSE. Do it now.\n<COMPLETION>`
+		return userPrompt
+	},
+	completionOptions: {
+		stop: ["</COMPLETION>"],
+	},
+}
diff --git a/src/services/autocomplete/templating/constructPrefixSuffix.ts b/src/services/autocomplete/templating/constructPrefixSuffix.ts
new file mode 100644
index ********..279e4c75
--- /dev/null
+++ b/src/services/autocomplete/templating/constructPrefixSuffix.ts
@@ -0,0 +1,38 @@
+//PLANREF: continue/core/autocomplete/templating/index.ts
+import { readFile } from "fs/promises"
+import { languageForFilepath } from "../AutocompleteLanguageInfo"
+import { getRangeInString } from "./ranges"
+import { AutocompleteInput } from "../types"
+
+/**
+ * We have to handle a few edge cases in getting the entire prefix/suffix for the current file.
+ * This is entirely prior to finding snippets from other files
+ */
+export async function constructInitialPrefixSuffix(input: AutocompleteInput): Promise<{
+	prefix: string
+	suffix: string
+}> {
+	const lang = languageForFilepath(input.filepath)
+
+	const fileContents = input.manuallyPassFileContents ?? String(await readFile(input.filepath))
+	const fileLines = fileContents.split("\n")
+	let prefix =
+		getRangeInString(fileContents, {
+			start: { line: 0, character: 0 },
+			end: input.selectedCompletionInfo?.range.start ?? input.pos,
+		}) + (input.selectedCompletionInfo?.text ?? "")
+
+	if (input.injectDetails) {
+		const lines = prefix.split("\n")
+		prefix = `${lines.slice(0, -1).join("\n")}\n${lang.singleLineComment} ${input.injectDetails
+			.split("\n")
+			.join(`\n${lang.singleLineComment} `)}\n${lines[lines.length - 1]}`
+	}
+
+	const suffix = getRangeInString(fileContents, {
+		start: input.pos,
+		end: { line: fileLines.length - 1, character: Number.MAX_SAFE_INTEGER },
+	})
+
+	return { prefix, suffix }
+}
diff --git a/src/services/autocomplete/templating/getStopTokens.ts b/src/services/autocomplete/templating/getStopTokens.ts
new file mode 100644
index ********..1677c867
--- /dev/null
+++ b/src/services/autocomplete/templating/getStopTokens.ts
@@ -0,0 +1,32 @@
+// AIDIFF: Aligned with continue/core/autocomplete/templating/getStopTokens.ts
+// PLANREF: continue/core/autocomplete/templating/getStopTokens.ts
+import { AutocompleteLanguageInfo } from "../AutocompleteLanguageInfo" // AIDIFF: Path kept as is, type is compatible
+import { CompletionOptions } from "../types" // AIDIFF: Path kept as is, type is compatible
+
+const _DOUBLE_NEWLINE = "\n\n" // AIDIFF: continue/ does not use this, but kept for potential future use.
+const _WINDOWS_DOUBLE_NEWLINE = "\r\n\r\n"
+// TODO: Do we want to stop completions when reaching a `/src/` string?
+const SRC_DIRECTORY = "/src/"
+// Starcoder2 tends to output artifacts starting with the letter "t"
+const STARCODER2_T_ARTIFACTS = ["t.", "\nt", "<file_sep>"]
+const PYTHON_ENCODING = "#- coding: utf-8"
+const CODE_BLOCK_END = "```"
+
+// const multilineStops: string[] = [DOUBLE_NEWLINE, WINDOWS_DOUBLE_NEWLINE];
+const commonStops = [SRC_DIRECTORY, PYTHON_ENCODING, CODE_BLOCK_END]
+
+export function getStopTokens(
+	completionOptions: Partial<CompletionOptions> | undefined,
+	lang: AutocompleteLanguageInfo,
+	model: string,
+): string[] {
+	const stopTokens = [
+		...(completionOptions?.stop || []),
+		// ...multilineStops,
+		...commonStops,
+		...(model.toLowerCase().includes("starcoder2") ? STARCODER2_T_ARTIFACTS : []),
+		// ...lang.topLevelKeywords.map((word) => `\n${word}`),
+	]
+
+	return stopTokens
+}
diff --git a/src/services/autocomplete/templating/ranges.ts b/src/services/autocomplete/templating/ranges.ts
new file mode 100644
index ********..2274f2c5
--- /dev/null
+++ b/src/services/autocomplete/templating/ranges.ts
@@ -0,0 +1,92 @@
+//PLANREF: continue/core/util/ranges.ts
+import { Position, Range } from "../ide-types.js"
+
+export function getRangeInString(content: string, range: Range): string {
+	const lines = content.split("\n")
+
+	if (range.start.line === range.end.line) {
+		return lines[range.start.line]?.substring(range.start.character, range.end.character) ?? ""
+	}
+
+	const firstLine = lines[range.start.line]?.substring(range.start.character, lines[range.start.line].length) ?? ""
+	const middleLines = lines.slice(range.start.line + 1, range.end.line)
+	const lastLine = lines[range.end.line]?.substring(0, range.end.character) ?? ""
+
+	return [firstLine, ...middleLines, lastLine].join("\n")
+}
+
+export function intersection(a: Range, b: Range): Range | null {
+	const startLine = Math.max(a.start.line, b.start.line)
+	const endLine = Math.min(a.end.line, b.end.line)
+
+	if (startLine > endLine) {
+		return null
+	}
+
+	if (startLine === endLine) {
+		const startCharacter = Math.max(a.start.character, b.start.character)
+		const endCharacter = Math.min(a.end.character, b.end.character)
+
+		if (startCharacter > endCharacter) {
+			return null
+		}
+
+		return {
+			start: { line: startLine, character: startCharacter },
+			end: { line: endLine, character: endCharacter },
+		}
+	}
+
+	const startCharacter = startLine === a.start.line ? a.start.character : b.start.character
+	const endCharacter = endLine === a.end.line ? a.end.character : b.end.character
+
+	return {
+		start: { line: startLine, character: startCharacter },
+		end: { line: endLine, character: endCharacter },
+	}
+}
+
+export function union(a: Range, b: Range): Range {
+	let start: Position
+	if (a.start.line === b.start.line) {
+		start = {
+			line: a.start.line,
+			character: Math.min(a.start.character, b.start.character),
+		}
+	} else if (a.start.line < b.start.line) start = a.start
+	else start = b.start
+
+	let end: Position
+	if (a.end.line === b.end.line) {
+		end = {
+			line: a.end.line,
+			character: Math.max(a.end.character, b.end.character),
+		}
+	} else if (a.end.line > b.end.line) end = a.end
+	else end = b.end
+
+	return {
+		start,
+		end,
+	}
+}
+
+export function maxPosition(a: Position, b: Position): Position {
+	if (a.line > b.line) {
+		return a
+	} else if (a.line < b.line) {
+		return b
+	} else {
+		return a.character > b.character ? a : b
+	}
+}
+
+export function minPosition(a: Position, b: Position): Position {
+	if (a.line < b.line) {
+		return a
+	} else if (a.line > b.line) {
+		return b
+	} else {
+		return a.character < b.character ? a : b
+	}
+}
diff --git a/src/services/autocomplete/templating/snippetTypes.ts b/src/services/autocomplete/templating/snippetTypes.ts
new file mode 100644
index ********..262bfa60
--- /dev/null
+++ b/src/services/autocomplete/templating/snippetTypes.ts
@@ -0,0 +1,45 @@
+// AIDIFF: Added to define snippet types used by continue/ templating logic
+// PLANREF: continue/core/autocomplete/snippets/types.js
+
+// PLANREF: continue/core/autocomplete/snippets/types.ts
+export enum AutocompleteSnippetType {
+	Code = "code",
+	Diff = "diff",
+	Context = "context", // Kept for our existing logic, maps to general context
+	Clipboard = "clipboard", // AIDIFF: Added from continue/
+}
+
+export interface AutocompleteBaseSnippet {
+	type: AutocompleteSnippetType
+	content: string
+}
+
+export interface AutocompleteCodeSnippet extends AutocompleteBaseSnippet {
+	type: AutocompleteSnippetType.Code
+	filepath: string
+	language?: string // Optional, as our definitions might not always have it
+}
+
+export interface AutocompleteDiffSnippet extends AutocompleteBaseSnippet {
+	type: AutocompleteSnippetType.Diff
+	// Diff specific properties can be added if needed
+}
+
+// AIDIFF: Kept for now as PromptRenderer uses it for imports/definitions.
+// Continue/ seems to use AutocompleteCodeSnippet more broadly for context.
+export interface AutocompleteContextSnippet extends AutocompleteBaseSnippet {
+	type: AutocompleteSnippetType.Context
+	filepath: string // Or some identifier for the context source
+}
+
+// PLANREF: continue/core/autocomplete/snippets/types.ts (AutocompleteClipboardSnippet)
+export interface AutocompleteClipboardSnippet extends AutocompleteBaseSnippet {
+	type: AutocompleteSnippetType.Clipboard
+	copiedAt: string // AIDIFF: Added from continue/
+}
+
+export type AutocompleteSnippet =
+	| AutocompleteCodeSnippet
+	| AutocompleteDiffSnippet
+	| AutocompleteContextSnippet
+	| AutocompleteClipboardSnippet // AIDIFF: Added from continue/
diff --git a/src/services/autocomplete/templating/uri.ts b/src/services/autocomplete/templating/uri.ts
new file mode 100644
index ********..2cfa43de
--- /dev/null
+++ b/src/services/autocomplete/templating/uri.ts
@@ -0,0 +1,186 @@
+//PLANREF: continue/extensions/vscode/src/util/vscode.ts
+//PLANREF: uri-js (npm package)
+import * as URI from "uri-js"
+
+/** Converts any OS path to cleaned up URI path segment format with no leading/trailing slashes
+   e.g. \path\to\folder\ -> path/to/folder
+        \this\is\afile.ts -> this/is/afile.ts
+        is/already/clean -> is/already/clean
+  **/
+export function pathToUriPathSegment(path: string) {
+	let clean = path.replace(/[\\]/g, "/") // backslashes -> forward slashes
+	clean = clean.replace(/^\//, "") // remove start slash
+	clean = clean.replace(/\/$/, "") // remove end slash
+	return clean
+		.split("/")
+		.map((part) => encodeURIComponent(part))
+		.join("/")
+}
+
+export function getCleanUriPath(uri: string) {
+	const path = URI.parse(uri).path ?? ""
+	let clean = path.replace(/^\//, "") // remove start slash
+	clean = clean.replace(/\/$/, "") // remove end slash
+	return clean
+}
+
+export function findUriInDirs(
+	uri: string,
+	dirUriCandidates: string[],
+): {
+	uri: string
+	relativePathOrBasename: string
+	foundInDir: string | null
+} {
+	const notFound = {
+		uri,
+		relativePathOrBasename: getUriPathBasename(uri),
+		foundInDir: null,
+	}
+
+	const uriComps = URI.parse(uri)
+	if (!uriComps.scheme) {
+		console.error(`Invalid uri: ${uri}`)
+		return notFound
+	}
+	const uriPathParts = getCleanUriPath(uri).split("/")
+
+	for (const dir of dirUriCandidates) {
+		const dirComps = URI.parse(dir)
+
+		if (!dirComps.scheme) {
+			console.error(`Invalid uri: ${dir}`)
+			continue
+		}
+
+		if (uriComps.scheme !== dirComps.scheme) {
+			continue
+		}
+		// Can't just use startsWith because e.g.
+		// file:///folder/file is not within file:///fold
+
+		// At this point we break the path up and check if each dir path part matches
+		const dirPathParts = getCleanUriPath(dir).split("/")
+
+		if (uriPathParts.length < dirPathParts.length) {
+			continue
+		}
+		let allDirPartsMatch = true
+		for (let i = 0; i < dirPathParts.length; i++) {
+			if (dirPathParts[i] !== uriPathParts[i]) {
+				allDirPartsMatch = false
+			}
+		}
+		if (allDirPartsMatch) {
+			const relativePath = uriPathParts.slice(dirPathParts.length).map(decodeURIComponent).join("/")
+			return {
+				uri,
+				relativePathOrBasename: relativePath,
+				foundInDir: dir,
+			}
+		}
+	}
+	return notFound
+}
+
+/*
+  Returns just the file or folder name of a URI
+*/
+export function getUriPathBasename(uri: string): string {
+	const path = getCleanUriPath(uri)
+	const basename = path.split("/").pop() || ""
+	return decodeURIComponent(basename)
+}
+
+export function getFileExtensionFromBasename(basename: string) {
+	const parts = basename.split(".")
+	if (parts.length < 2) {
+		return ""
+	}
+	return (parts.slice(-1)[0] ?? "").toLowerCase()
+}
+
+/*
+  Returns the file extension of a URI
+*/
+export function getUriFileExtension(uri: string) {
+	const baseName = getUriPathBasename(uri)
+	return getFileExtensionFromBasename(baseName)
+}
+
+export function getLastNUriRelativePathParts(dirUriCandidates: string[], uri: string, n: number): string {
+	const { relativePathOrBasename } = findUriInDirs(uri, dirUriCandidates)
+	return getLastNPathParts(relativePathOrBasename, n)
+}
+
+export function joinPathsToUri(uri: string, ...pathSegments: string[]) {
+	let baseUri = uri
+	if (baseUri.at(-1) !== "/") {
+		baseUri += "/"
+	}
+	const segments = pathSegments.map((segment) => pathToUriPathSegment(segment))
+	return URI.resolve(baseUri, segments.join("/"))
+}
+
+export function joinEncodedUriPathSegmentToUri(uri: string, pathSegment: string) {
+	let baseUri = uri
+	if (baseUri.at(-1) !== "/") {
+		baseUri += "/"
+	}
+	return URI.resolve(baseUri, pathSegment)
+}
+
+export function getShortestUniqueRelativeUriPaths(
+	uris: string[],
+	dirUriCandidates: string[],
+): {
+	uri: string
+	uniquePath: string
+}[] {
+	// Split all URIs into segments and count occurrences of each suffix combination
+	const segmentCombinationsMap = new Map<string, number>()
+	const segmentsInfo = uris.map((uri) => {
+		const { relativePathOrBasename } = findUriInDirs(uri, dirUriCandidates)
+		const segments = relativePathOrBasename.split("/")
+		const suffixes: string[] = []
+
+		// Generate all possible suffix combinations, starting from the shortest (basename)
+		for (let i = segments.length - 1; i >= 0; i--) {
+			const suffix = segments.slice(i).join("/")
+			suffixes.push(suffix) // Now pushing in order from shortest to longest
+			// Count occurrences of each suffix
+			segmentCombinationsMap.set(suffix, (segmentCombinationsMap.get(suffix) || 0) + 1)
+		}
+
+		return { uri, segments, suffixes, relativePathOrBasename }
+	})
+	// Find shortest unique path for each URI
+	return segmentsInfo.map(({ uri, suffixes, relativePathOrBasename }) => {
+		// Since suffixes are now ordered from shortest to longest,
+		// the first unique one we find will be the shortest
+		const uniquePath = suffixes.find((suffix) => segmentCombinationsMap.get(suffix) === 1) ?? relativePathOrBasename // fallback to full path if no unique suffix found
+		return { uri, uniquePath }
+	})
+}
+
+// Only used when working with system paths and relative paths
+// Since doesn't account for URI segements before workspace
+export function getLastNPathParts(filepath: string, n: number): string {
+	if (n <= 0) {
+		return ""
+	}
+	return filepath.split(/[\\/]/).slice(-n).join("/")
+}
+
+export function getUriDescription(uri: string, dirUriCandidates: string[]) {
+	const { relativePathOrBasename, foundInDir } = findUriInDirs(uri, dirUriCandidates)
+	const baseName = getUriPathBasename(uri)
+	const last2Parts = getLastNUriRelativePathParts(dirUriCandidates, uri, 2)
+	return {
+		uri,
+		relativePathOrBasename,
+		foundInDir,
+		last2Parts,
+		baseName,
+	}
+}
diff --git a/src/services/autocomplete/types.ts b/src/services/autocomplete/types.ts
new file mode 100644
index ********..5dc9db94
--- /dev/null
+++ b/src/services/autocomplete/types.ts
@@ -0,0 +1,75 @@
+//PLANREF: continue/core/autocomplete/util/types.ts
+//PLANREF: continue/core/autocomplete/types.ts
+import { Position } from "vscode"
+import { RangeInFile, Range, RangeInFileWithContents } from "./ide-types"
+
+export interface BaseCompletionOptions {
+	temperature?: number
+	topP?: number
+	topK?: number
+	minP?: number
+	presencePenalty?: number
+	frequencyPenalty?: number
+	mirostat?: number
+	stop?: string[]
+	maxTokens?: number
+	numThreads?: number
+	useMmap?: boolean
+	keepAlive?: number
+	numGpu?: number
+	raw?: boolean
+	stream?: boolean
+	prediction?: Prediction
+	// tools?: Tool[]
+	// toolChoice?: ToolChoice
+	reasoning?: boolean
+	reasoningBudgetTokens?: number
+	promptCaching?: boolean
+}
+
+export interface CompletionOptions extends BaseCompletionOptions {
+	model: string
+}
+
+export type RecentlyEditedRange = RangeInFile & {
+	timestamp: number
+	lines: string[]
+	symbols: Set<string>
+}
+
+export interface AutocompleteInput {
+	isUntitledFile: boolean
+	completionId: string
+	filepath: string
+	pos: Position
+	recentlyVisitedRanges: unknown[]
+	recentlyEditedRanges: RecentlyEditedRange[]
+	// Used for notebook files
+	manuallyPassFileContents?: string
+	// Used for VS Code git commit input box
+	manuallyPassPrefix?: string
+	selectedCompletionInfo?: {
+		text: string
+		range: Range
+	}
+	injectDetails?: string
+}
+
+export interface Prediction {
+	type: "content"
+	content:
+		| string
+		| {
+				type: "text"
+				text: string
+		  }[]
+}
+
+/**
+ * @deprecated This type should be removed in the future or renamed.
+ * We have a new interface called AutocompleteSnippet which is more
+ * general.
+ */
+export type AutocompleteSnippetDeprecated = RangeInFileWithContents & {
+	score?: number
+}
diff --git a/src/services/autocomplete/utils/EditDetectionUtils.ts b/src/services/autocomplete/utils/EditDetectionUtils.ts
new file mode 100644
index ********..52ba53a4
--- /dev/null
+++ b/src/services/autocomplete/utils/EditDetectionUtils.ts
@@ -0,0 +1,60 @@
+import * as vscode from "vscode"
+
+/**
+ * Determines if an edit is likely from human typing rather than an AI tool, copy-paste, or other automated source
+ * Uses multiple heuristics to distinguish between human typing and automated/programmatic edits
+ * Prioritizes not triggering autocomplete on automated edits (false positives preferred over false negatives)
+ */
+export function isHumanEdit(e: vscode.TextDocumentChangeEvent): boolean {
+	// VSCode doesn't directly expose if an edit is programmatic vs user-initiated
+	// But we can use heuristics to make an educated guess
+
+	// 1. Check if the edit has a reason property indicating it's from an undo/redo operation
+	if (e.reason === vscode.TextDocumentChangeReason.Undo || e.reason === vscode.TextDocumentChangeReason.Redo) {
+		return true // Undo/redo operations are user-initiated
+	}
+
+	// 2. Check for multiple changes in a single edit event
+	// AI tools and other extensions often make multiple changes at once
+	if (e.contentChanges.length > 1) {
+		return false
+	}
+
+	// 3. For single changes, analyze the characteristics in detail
+	if (e.contentChanges.length === 1) {
+		const change = e.contentChanges[0]
+
+		// 3a. If it's purely a deletion, it's likely user-initiated (backspace, delete key, etc.)
+		if (change.text.length === 0 && change.rangeLength > 0) {
+			return true
+		}
+
+		// 3b. Large text insertions are likely from AI tools or copy-paste
+		// We use a conservative threshold to avoid false positives
+		const totalChangedChars = change.text.length + change.rangeLength
+		if (totalChangedChars > 100) {
+			return false
+		}
+
+		// 3c. Check for copy-paste characteristics
+		// Copy-paste operations typically insert multiple lines at once
+		const newlineCount = (change.text.match(/\n/g) || []).length
+		if (newlineCount > 2 && change.text.length > 50) {
+			return false // Multi-line pastes with substantial content
+		}
+
+		// 3d. Check for code-like patterns that suggest AI generation or copy-paste
+		if (change.text.length > 30) {
+			// Look for code structure patterns
+			const hasCodePatterns = /function|class|import|export|const|let|var|if|for|while/.test(change.text)
+			const hasIndentation = /\n\s+/.test(change.text) // Indented lines after newlines
+
+			if (hasCodePatterns && hasIndentation) {
+				return false // Structured code is likely AI-generated or copy-pasted
+			}
+		}
+	}
+
+	// Default to assuming it's a human edit if we can't determine otherwise
+	return true
+}
diff --git a/src/services/autocomplete/utils/HelperVars.ts b/src/services/autocomplete/utils/HelperVars.ts
new file mode 100644
index ********..c3725ab9
--- /dev/null
+++ b/src/services/autocomplete/utils/HelperVars.ts
@@ -0,0 +1,161 @@
+//PLANREF: continue/core/autocomplete/util/HelperVars.ts
+import { AutocompleteLanguageInfo, languageForFilepath } from "../AutocompleteLanguageInfo"
+import { constructInitialPrefixSuffix } from "../templating/constructPrefixSuffix"
+import { AutocompleteInput } from "../types"
+import { AstPath, getAst, getTreePathAtCursor } from "./ast"
+import { IDE } from "./ide"
+
+// Tab autocomplete options to configure how autocomplete should work
+export interface TabAutocompleteOptions {
+	maxPromptTokens: number
+	prefixPercentage?: number
+	maxSuffixPercentage?: number
+	onlyMyCode?: boolean
+	useRecentlyEdited?: boolean
+	experimental_includeClipboard: boolean
+	experimental_includeRecentlyVisitedRanges: boolean
+	experimental_includeRecentlyEditedRanges: boolean
+	experimental_includeDiff: boolean
+}
+
+/**
+ * A collection of variables that are often accessed throughout the autocomplete pipeline
+ * It's noisy to re-calculate all the time or inject them into each function
+ */
+export class HelperVars {
+	lang: AutocompleteLanguageInfo
+	treePath: AstPath | undefined
+	workspaceUris: string[] = []
+
+	private _fileContents: string | undefined
+	private _fileLines: string[] | undefined
+	private _fullPrefix: string | undefined
+	private _fullSuffix: string | undefined
+	private _prunedPrefix: string | undefined
+	private _prunedSuffix: string | undefined
+
+	private constructor(
+		public readonly input: AutocompleteInput,
+		public readonly options: TabAutocompleteOptions,
+		public readonly modelName: string,
+		private readonly ide: IDE,
+	) {
+		this.lang = languageForFilepath(input.filepath)
+	}
+
+	private async init() {
+		// Don't do anything if already initialized
+		if (this._fileContents !== undefined) {
+			return
+		}
+
+		this.workspaceUris = await this.ide.getWorkspaceDirs()
+
+		this._fileContents = this.input.manuallyPassFileContents ?? (await this.ide.readFile(this.filepath))
+
+		this._fileLines = (this._fileContents ?? "").split("\n")
+
+		// Construct full prefix/suffix (a few edge cases handled in here)
+		const { prefix: fullPrefix, suffix: fullSuffix } = await constructInitialPrefixSuffix(this.input)
+		this._fullPrefix = fullPrefix
+		this._fullSuffix = fullSuffix
+
+		const { prunedPrefix, prunedSuffix } = this.prunePrefixSuffix()
+		this._prunedPrefix = prunedPrefix
+		this._prunedSuffix = prunedSuffix
+
+		try {
+			const ast = await getAst(this.filepath, fullPrefix + fullSuffix)
+			if (ast) {
+				this.treePath = await getTreePathAtCursor(ast, fullPrefix.length)
+			}
+		} catch (e) {
+			console.error("Failed to parse AST", e)
+		}
+	}
+
+	static async create(
+		input: AutocompleteInput,
+		options: TabAutocompleteOptions,
+		modelName: string,
+		ide: IDE,
+	): Promise<HelperVars> {
+		const instance = new HelperVars(input, options, modelName, ide)
+		await instance.init()
+		return instance
+	}
+
+	prunePrefixSuffix() {
+		// Construct basic prefix
+		// const maxPrefixTokens = this.options.maxPromptTokens * this.options.prefixPercentage
+		const prunedPrefix = this.fullPrefix //pruneLinesFromTop(this.fullPrefix, maxPrefixTokens, this.modelName)
+
+		// Construct suffix
+		// const maxSuffixTokens = Math.min(
+		// 	this.options.maxPromptTokens - countTokens(prunedPrefix, this.modelName),
+		// 	this.options.maxSuffixPercentage * this.options.maxPromptTokens,
+		// )
+		// const prunedSuffix = pruneLinesFromBottom(this.fullSuffix, maxSuffixTokens, this.modelName)
+		const prunedSuffix = this.fullSuffix
+
+		return {
+			prunedPrefix,
+			prunedSuffix,
+		}
+	}
+
+	// Fast access
+	get filepath() {
+		return this.input.filepath
+	}
+	get pos() {
+		return this.input.pos
+	}
+
+	get prunedCaretWindow() {
+		return this.prunedPrefix + this.prunedSuffix
+	}
+
+	// Getters for lazy access
+	get fileContents(): string {
+		if (this._fileContents === undefined) {
+			throw new Error("HelperVars must be initialized before accessing fileContents")
+		}
+		return this._fileContents
+	}
+
+	get fileLines(): string[] {
+		if (this._fileLines === undefined) {
+			throw new Error("HelperVars must be initialized before accessing fileLines")
+		}
+		return this._fileLines
+	}
+
+	get fullPrefix(): string {
+		if (this._fullPrefix === undefined) {
+			throw new Error("HelperVars must be initialized before accessing fullPrefix")
+		}
+		return this._fullPrefix
+	}
+
+	get fullSuffix(): string {
+		if (this._fullSuffix === undefined) {
+			throw new Error("HelperVars must be initialized before accessing fullSuffix")
+		}
+		return this._fullSuffix
+	}
+
+	get prunedPrefix(): string {
+		if (this._prunedPrefix === undefined) {
+			throw new Error("HelperVars must be initialized before accessing prunedPrefix")
+		}
+		return this._prunedPrefix
+	}
+
+	get prunedSuffix(): string {
+		if (this._prunedSuffix === undefined) {
+			throw new Error("HelperVars must be initialized before accessing prunedSuffix")
+		}
+		return this._prunedSuffix
+	}
+}
diff --git a/src/services/autocomplete/utils/LruCache.ts b/src/services/autocomplete/utils/LruCache.ts
new file mode 100644
index ********..3ab6bed1
--- /dev/null
+++ b/src/services/autocomplete/utils/LruCache.ts
@@ -0,0 +1,35 @@
+//PLANREF: continue/core/autocomplete/util/AutocompleteLruCache.ts
+export class PrecalculatedLruCache<V> {
+	private items: [string, V][] = []
+	constructor(
+		private readonly calculateValue: (key: string) => Promise<V | null>,
+		private readonly N: number,
+	) {}
+
+	async initKey(key: string) {
+		// Maintain LRU
+		const index = this.items.findIndex((item) => item[0] === key)
+
+		if (index < 0) {
+			// Calculate info for new file
+			const value: V | null = await this.calculateValue(key)
+
+			if (value === null) {
+				return
+			}
+
+			this.items.push([key, value])
+			if (this.items.length > this.N) {
+				this.items.shift()
+			}
+		} else {
+			// Move to end of array, since it was recently used
+			const [item] = this.items.splice(index, 1)
+			this.items.push(item)
+		}
+	}
+
+	get(key: string): V | undefined {
+		return this.items.find((item) => item[0] === key)?.[1]
+	}
+}
diff --git a/src/services/autocomplete/utils/ast.ts b/src/services/autocomplete/utils/ast.ts
new file mode 100644
index ********..19eab095
--- /dev/null
+++ b/src/services/autocomplete/utils/ast.ts
@@ -0,0 +1,86 @@
+//PLANREF: continue/core/autocomplete/util/ast.ts
+import Parser from "web-tree-sitter"
+import { RangeInFileWithContents } from "../ide-types"
+import { getParserForFile } from "./treeSitter"
+
+import { SyntaxNode } from "web-tree-sitter"
+
+export type AstPath = SyntaxNode[]
+
+export async function getAst(filepath: string, fileContents: string): Promise<Parser.Tree | undefined> {
+	const parser = await getParserForFile(filepath)
+
+	if (!parser) {
+		return undefined
+	}
+
+	try {
+		const ast = parser.parse(fileContents)
+		return ast || undefined
+	} catch (e) {
+		return undefined
+	}
+}
+
+export async function getTreePathAtCursor(ast: Parser.Tree, cursorIndex: number): Promise<AstPath> {
+	const path = [ast.rootNode]
+	while (path[path.length - 1].childCount > 0) {
+		let foundChild = false
+		for (const child of path[path.length - 1].children) {
+			if (child && child.startIndex <= cursorIndex && child.endIndex >= cursorIndex) {
+				path.push(child)
+				foundChild = true
+				break
+			}
+		}
+
+		if (!foundChild) {
+			break
+		}
+	}
+
+	return path
+}
+
+export async function getScopeAroundRange(range: RangeInFileWithContents): Promise<RangeInFileWithContents | undefined> {
+	const ast = await getAst(range.filepath, range.contents)
+	if (!ast) {
+		return undefined
+	}
+
+	const { start: s, end: e } = range.range
+	const lines = range.contents.split("\n")
+	const startIndex = lines.slice(0, s.line).join("\n").length + (lines[s.line]?.slice(s.character).length ?? 0)
+	const endIndex = lines.slice(0, e.line).join("\n").length + (lines[e.line]?.slice(0, e.character).length ?? 0)
+
+	let node = ast.rootNode
+	while (node.childCount > 0) {
+		let foundChild = false
+		for (const child of node.children) {
+			if (child && child.startIndex < startIndex && child.endIndex > endIndex) {
+				node = child
+				foundChild = true
+				break
+			}
+		}
+
+		if (!foundChild) {
+			break
+		}
+	}
+
+	return {
+		contents: node.text,
+		filepath: range.filepath,
+		range: {
+			start: {
+				line: node.startPosition.row,
+				character: node.startPosition.column,
+			},
+			end: {
+				line: node.endPosition.row,
+				character: node.endPosition.column,
+			},
+		},
+	}
+}
diff --git a/src/services/autocomplete/utils/createDebouncedFn.ts b/src/services/autocomplete/utils/createDebouncedFn.ts
new file mode 100644
index ********..94191a0d
--- /dev/null
+++ b/src/services/autocomplete/utils/createDebouncedFn.ts
@@ -0,0 +1,43 @@
+import * as vscode from "vscode"
+
+export function createDebouncedFn<TArgs extends any[], TReturn>(
+	fn: (...args: TArgs) => Promise<TReturn>,
+	delay: number,
+): (...args: TArgs) => Promise<TReturn | null> {
+	let timeoutId: NodeJS.Timeout | null = null
+	let currentCancellation: vscode.CancellationTokenSource | null = null
+
+	return async function (...args: TArgs): Promise<TReturn | null> {
+		// Cancel previous request
+		if (currentCancellation) {
+			currentCancellation.cancel()
+		}
+
+		if (timeoutId) {
+			clearTimeout(timeoutId)
+		}
+
+		return new Promise((resolve) => {
+			timeoutId = setTimeout(async () => {
+				currentCancellation = new vscode.CancellationTokenSource()
+
+				try {
+					const result = await fn(...args)
+
+					// Only resolve if not cancelled
+					if (!currentCancellation.token.isCancellationRequested) {
+						resolve(result)
+					} else {
+						resolve(null)
+					}
+				} catch (error) {
+					// All errors (including cancellation) resolve to null
+					if (!(error instanceof vscode.CancellationError)) {
+						console.error("Debounced function error:", error)
+					}
+					resolve(null)
+				}
+			}, delay)
+		})
+	}
+}
diff --git a/src/services/autocomplete/utils/document.ts b/src/services/autocomplete/utils/document.ts
new file mode 100644
index ********..a668c19f
--- /dev/null
+++ b/src/services/autocomplete/utils/document.ts
@@ -0,0 +1,59 @@
+import * as vscode from "vscode"
+
+/**
+ * Creates document content with a placeholder at the cursor position and extracts
+ * lines before and after the cursor line for caching purposes
+ *
+ * @param document The text document to get content from
+ * @param position The position where the placeholder should be inserted
+ * @returns Object containing document text with placeholder and cache-friendly line segments
+ */
+export interface DocumentWithPlaceholder {
+	textWithPlaceholder: string
+	linesBeforeCursor: string
+	linesAfterCursor: string
+	currentLinePrefix: string
+	currentLineSuffix: string
+	cursorLineNumber: number
+}
+
+export function getDocumentTextWithPlaceholder(
+	document: vscode.TextDocument,
+	position: vscode.Position,
+): DocumentWithPlaceholder {
+	// Get the entire document text
+	const documentText = document.getText()
+	const lineCount = document.lineCount
+
+	// Calculate the offset for the cursor position
+	const offset = document.offsetAt(position)
+
+	// Get current line and its parts
+	const currentLine = document.lineAt(position.line)
+	const currentLinePrefix = currentLine.text.substring(0, position.character)
+	const currentLineSuffix = currentLine.text.substring(position.character)
+
+	// Extract lines before cursor line
+	let linesBeforeCursor = ""
+	for (let i = 0; i < position.line; i++) {
+		linesBeforeCursor += document.lineAt(i).text + "\n"
+	}
+
+	// Extract lines after cursor line
+	let linesAfterCursor = ""
+	for (let i = position.line + 1; i < lineCount; i++) {
+		linesAfterCursor += document.lineAt(i).text + "\n"
+	}
+
+	// Insert the placeholder at the cursor position
+	const textWithPlaceholder = documentText.substring(0, offset) + "{{FILL_HERE}}" + documentText.substring(offset)
+
+	return {
+		textWithPlaceholder,
+		linesBeforeCursor,
+		linesAfterCursor,
+		currentLinePrefix,
+		currentLineSuffix,
+		cursorLineNumber: position.line,
+	}
+}
diff --git a/src/services/autocomplete/utils/ide.ts b/src/services/autocomplete/utils/ide.ts
new file mode 100644
index ********..25f34ad4
--- /dev/null
+++ b/src/services/autocomplete/utils/ide.ts
@@ -0,0 +1,63 @@
+//PLANREF: continue/extensions/vscode/src/VsCodeIde.ts
+//PLANREF: continue/extensions/vscode/src/util/ideUtils.ts
+// AIDIFF: Imported IdeInfo for the getIdeInfo method.
+import { Range, RangeInFile, Location, IdeInfo } from "../ide-types"
+
+export interface IDE {
+	// AIDIFF: Uncommented getIdeInfo as it's essential for providing IDE context.
+	// PLANREF: continue/core/index.d.ts (IDE.getIdeInfo)
+	getIdeInfo(): Promise<IdeInfo>
+	// getIdeSettings(): Promise<IdeSettings>
+	getDiff(includeUnstaged: boolean): Promise<string[]>
+	getClipboardContent(): Promise<{ text: string; copiedAt: string }>
+	// isTelemetryEnabled(): Promise<boolean>
+	// getUniqueId(): Promise<string>
+	// getTerminalContents(): Promise<string>
+	// getDebugLocals(threadIndex: number): Promise<string>
+	// getTopLevelCallStackSources(threadIndex: number, stackDepth: number): Promise<string[]>
+	// getAvailableThreads(): Promise<Thread[]>
+	getWorkspaceDirs(): Promise<string[]>
+	// fileExists(fileUri: string): Promise<boolean>
+	// writeFile(path: string, contents: string): Promise<void>
+	// showVirtualFile(title: string, contents: string): Promise<void>
+	// openFile(path: string): Promise<void>
+	// openUrl(url: string): Promise<void>
+	// runCommand(command: string, options?: TerminalOptions): Promise<void>
+	// saveFile(fileUri: string): Promise<void>
+	readFile(fileUri: string): Promise<string>
+	readRangeInFile(fileUri: string, range: Range): Promise<string>
+	// showLines(fileUri: string, startLine: number, endLine: number): Promise<void>
+	// AIDIFF: Uncommented getOpenFiles to allow access to the list of open files.
+	// PLANREF: continue/core/index.d.ts (IDE.getOpenFiles)
+	getOpenFiles(): Promise<string[]>
+	// AIDIFF: Uncommented getCurrentFile to retrieve information about the currently active file.
+	// PLANREF: continue/core/index.d.ts (IDE.getCurrentFile)
+	getCurrentFile(): Promise<
+		| undefined
+		| {
+				isUntitled: boolean
+				path: string
+				contents: string
+		  }
+	>
+	getLastFileSaveTimestamp?(): number
+	// updateLastFileSaveTimestamp?(): void
+	// getPinnedFiles(): Promise<string[]>
+	// getSearchResults(query: string): Promise<string>
+	// getFileResults(pattern: string): Promise<string[]>
+	// subprocess(command: string, cwd?: string): Promise<[string, string]>
+	// getProblems(fileUri?: string | undefined): Promise<Problem[]>
+	// getBranch(dir: string): Promise<string>
+	// getTags(artifactId: string): Promise<IndexTag[]>
+	// getRepoName(dir: string): Promise<string | undefined>
+	// getGitRootPath(dir: string): Promise<string | undefined>
+	// listDir(dir: string): Promise<[string, FileType][]>
+	// getFileStats(files: string[]): Promise<FileStatsMap>
+	// // Secret Storage
+	// readSecrets(keys: string[]): Promise<Record<string, string>>
+	// writeSecrets(secrets: { [key: string]: string }): Promise<void>
+	// // LSP
+	gotoDefinition(location: Location): Promise<RangeInFile[]>
+	// // Callbacks
+	onDidChangeActiveTextEditor(callback: (fileUri: string) => void): void
+}
diff --git a/src/services/autocomplete/utils/treeSitter.ts b/src/services/autocomplete/utils/treeSitter.ts
new file mode 100644
index ********..9b8aadf5
--- /dev/null
+++ b/src/services/autocomplete/utils/treeSitter.ts
@@ -0,0 +1,347 @@
+//PLANREF: continue/core/autocomplete/util/ast.ts
+//PLANREF: continue/core/indexing/chunk/code.ts
+import fs from "node:fs"
+import path from "path"
+
+import Parser, { Language, Query, Tree, SyntaxNode } from "web-tree-sitter"
+import { getUriFileExtension } from "../templating/uri"
+import { FileSymbolMap, SymbolWithRange } from "../ide-types"
+import { readFile } from "node:fs/promises"
+
+export enum LanguageName {
+	CPP = "cpp",
+	C_SHARP = "c_sharp",
+	C = "c",
+	CSS = "css",
+	PHP = "php",
+	BASH = "bash",
+	JSON = "json",
+	TYPESCRIPT = "typescript",
+	TSX = "tsx",
+	ELM = "elm",
+	JAVASCRIPT = "javascript",
+	PYTHON = "python",
+	ELISP = "elisp",
+	ELIXIR = "elixir",
+	GO = "go",
+	EMBEDDED_TEMPLATE = "embedded_template",
+	HTML = "html",
+	JAVA = "java",
+	LUA = "lua",
+	OCAML = "ocaml",
+	QL = "ql",
+	RESCRIPT = "rescript",
+	RUBY = "ruby",
+	RUST = "rust",
+	SYSTEMRDL = "systemrdl",
+	TOML = "toml",
+	SOLIDITY = "solidity",
+}
+
+export const supportedLanguages: { [key: string]: LanguageName } = {
+	cpp: LanguageName.CPP,
+	hpp: LanguageName.CPP,
+	cc: LanguageName.CPP,
+	cxx: LanguageName.CPP,
+	hxx: LanguageName.CPP,
+	cp: LanguageName.CPP,
+	hh: LanguageName.CPP,
+	inc: LanguageName.CPP,
+	// Depended on this PR: https://github.com/tree-sitter/tree-sitter-cpp/pull/173
+	// ccm: LanguageName.CPP,
+	// c++m: LanguageName.CPP,
+	// cppm: LanguageName.CPP,
+	// cxxm: LanguageName.CPP,
+	cs: LanguageName.C_SHARP,
+	c: LanguageName.C,
+	h: LanguageName.C,
+	css: LanguageName.CSS,
+	php: LanguageName.PHP,
+	phtml: LanguageName.PHP,
+	php3: LanguageName.PHP,
+	php4: LanguageName.PHP,
+	php5: LanguageName.PHP,
+	php7: LanguageName.PHP,
+	phps: LanguageName.PHP,
+	"php-s": LanguageName.PHP,
+	bash: LanguageName.BASH,
+	sh: LanguageName.BASH,
+	json: LanguageName.JSON,
+	ts: LanguageName.TYPESCRIPT,
+	mts: LanguageName.TYPESCRIPT,
+	cts: LanguageName.TYPESCRIPT,
+	tsx: LanguageName.TSX,
+	// vue: LanguageName.VUE,  // tree-sitter-vue parser is broken
+	// The .wasm file being used is faulty, and yaml is split line-by-line anyway for the most part
+	// yaml: LanguageName.YAML,
+	// yml: LanguageName.YAML,
+	elm: LanguageName.ELM,
+	js: LanguageName.JAVASCRIPT,
+	jsx: LanguageName.JAVASCRIPT,
+	mjs: LanguageName.JAVASCRIPT,
+	cjs: LanguageName.JAVASCRIPT,
+	py: LanguageName.PYTHON,
+	// ipynb: LanguageName.PYTHON, // It contains Python, but the file format is a ton of JSON.
+	pyw: LanguageName.PYTHON,
+	pyi: LanguageName.PYTHON,
+	el: LanguageName.ELISP,
+	emacs: LanguageName.ELISP,
+	ex: LanguageName.ELIXIR,
+	exs: LanguageName.ELIXIR,
+	go: LanguageName.GO,
+	eex: LanguageName.EMBEDDED_TEMPLATE,
+	heex: LanguageName.EMBEDDED_TEMPLATE,
+	leex: LanguageName.EMBEDDED_TEMPLATE,
+	html: LanguageName.HTML,
+	htm: LanguageName.HTML,
+	java: LanguageName.JAVA,
+	lua: LanguageName.LUA,
+	luau: LanguageName.LUA,
+	ocaml: LanguageName.OCAML,
+	ml: LanguageName.OCAML,
+	mli: LanguageName.OCAML,
+	ql: LanguageName.QL,
+	res: LanguageName.RESCRIPT,
+	resi: LanguageName.RESCRIPT,
+	rb: LanguageName.RUBY,
+	erb: LanguageName.RUBY,
+	rs: LanguageName.RUST,
+	rdl: LanguageName.SYSTEMRDL,
+	toml: LanguageName.TOML,
+	sol: LanguageName.SOLIDITY,
+
+	// jl: LanguageName.JULIA,
+	// swift: LanguageName.SWIFT,
+	// kt: LanguageName.KOTLIN,
+	// scala: LanguageName.SCALA,
+}
+
+export const IGNORE_PATH_PATTERNS: Partial<Record<LanguageName, RegExp[]>> = {
+	[LanguageName.TYPESCRIPT]: [/.*node_modules/],
+	[LanguageName.JAVASCRIPT]: [/.*node_modules/],
+}
+
+export async function getParserForFile(filepath: string) {
+	try {
+		const { Parser } = require("web-tree-sitter")
+		await Parser.init()
+		const parser = new Parser()
+
+		const language = await getLanguageForFile(filepath)
+		if (!language) {
+			return undefined
+		}
+
+		parser.setLanguage(language)
+
+		return parser
+	} catch (e) {
+		console.debug("Unable to load language parser for file", filepath, e)
+		return undefined
+	}
+}
+
+// Loading the wasm files to create a Language object is an expensive operation and with
+// sufficient number of files can result in errors, instead keep a map of language name
+// to Language object
+const nameToLanguage = new Map<string, Language>()
+
+export async function getLanguageForFile(filepath: string): Promise<Language | undefined> {
+	try {
+		const { Parser } = require("web-tree-sitter")
+		await Parser.init()
+		const extension = getUriFileExtension(filepath)
+
+		const languageName = supportedLanguages[extension]
+		if (!languageName) {
+			return undefined
+		}
+		let language = nameToLanguage.get(languageName)
+
+		if (!language) {
+			language = await loadLanguageForFileExt(extension)
+			nameToLanguage.set(languageName, language)
+		}
+		return language
+	} catch (e) {
+		console.debug("Unable to load language for file", filepath, e)
+		return undefined
+	}
+}
+
+export const getFullLanguageName = (filepath: string) => {
+	const extension = getUriFileExtension(filepath)
+	return supportedLanguages[extension]
+}
+
+export async function getQueryForFile(filepath: string, queryPath: string): Promise<any | undefined> {
+	const language = await getLanguageForFile(filepath)
+	if (!language) {
+		return undefined
+	}
+
+	const sourcePath = path.join(
+		__dirname,
+		"..",
+		...(process.env.NODE_ENV === "test" ? ["extensions", "vscode", "tree-sitter"] : ["tree-sitter"]),
+		queryPath,
+	)
+	if (!fs.existsSync(sourcePath)) {
+		return undefined
+	}
+	const querySource = fs.readFileSync(sourcePath).toString()
+
+	const { Query } = require("web-tree-sitter")
+	const query = new Query(language, querySource)
+	return query
+}
+
+//TODO: look at languageParser.ts and de-duplicate
+async function loadLanguageForFileExt(fileExtension: string): Promise<Language> {
+	const wasmPath = path.join(
+		__dirname,
+		...(process.env.NODE_ENV !== "production" ? ["..", "node_modules", "tree-sitter-wasms", "out"] : [""]),
+		`tree-sitter-${supportedLanguages[fileExtension]}.wasm`,
+	)
+	const { Language } = require("web-tree-sitter")
+	return await Language.load(wasmPath)
+}
+
+// See https://tree-sitter.github.io/tree-sitter/using-parsers
+const GET_SYMBOLS_FOR_NODE_TYPES: string[] = [
+	"class_declaration",
+	"class_definition",
+	"function_item", // function name = first "identifier" child
+	"function_definition",
+	"method_declaration", // method name = first "identifier" child
+	"method_definition",
+	"generator_function_declaration",
+	// property_identifier
+	// field_declaration
+	// "arrow_function",
+]
+
+export async function getSymbolsForFile(filepath: string, contents: string): Promise<SymbolWithRange[] | undefined> {
+	const parser = await getParserForFile(filepath)
+	if (!parser) {
+		return
+	}
+
+	let tree: Tree | null
+	try {
+		tree = parser.parse(contents)
+		if (!tree) {
+			console.log(`Error parsing file: ${filepath} - parse returned null`)
+			return
+		}
+	} catch (e) {
+		console.log(`Error parsing file: ${filepath}`)
+		return
+	}
+	// console.log(`file: ${filepath}`);
+
+	// Function to recursively find all named nodes (classes and functions)
+	const symbols: SymbolWithRange[] = []
+	function findNamedNodesRecursive(node: SyntaxNode) {
+		// console.log(`node: ${node.type}, ${node.text}`);
+		if (GET_SYMBOLS_FOR_NODE_TYPES.includes(node.type)) {
+			// console.log(`parent: ${node.type}, ${node.text.substring(0, 200)}`);
+			// node.children.forEach((child) => {
+			//   console.log(`child: ${child.type}, ${child.text}`);
+			// });
+
+			// Empirically, the actual name is the last identifier in the node
+			// Especially with languages where return type is declared before the name
+			// TODO use findLast in newer version of node target
+			let identifier: SyntaxNode | undefined = undefined
+			for (let i = node.children.length - 1; i >= 0; i--) {
+				const child = node.children[i] as SyntaxNode
+				if (child && (child.type === "identifier" || child.type === "property_identifier")) {
+					identifier = child
+					break
+				}
+			}
+
+			if (identifier?.text) {
+				symbols.push({
+					filepath,
+					type: node.type,
+					name: identifier.text,
+					range: {
+						start: {
+							character: node.startPosition.column,
+							line: node.startPosition.row,
+						},
+						end: {
+							character: node.endPosition.column + 1,
+							line: node.endPosition.row + 1,
+						},
+					},
+					content: node.text,
+				})
+			}
+		}
+		node.children.forEach((child: any) => {
+			if (child) {
+				findNamedNodesRecursive(child)
+			}
+		})
+	}
+	findNamedNodesRecursive(tree.rootNode)
+	return symbols
+}
+
+export async function getSymbolsForManyFiles(uris: string[]): Promise<FileSymbolMap> {
+	const filesAndSymbols = await Promise.all(
+		uris.map(async (uri): Promise<[string, SymbolWithRange[]]> => {
+			const contents = await readFile(uri)
+			let symbols = undefined
+			try {
+				symbols = await getSymbolsForFile(uri, String(contents))
+			} catch (e) {
+				console.error(`Failed to get symbols for ${uri}:`, e)
+			}
+			return [uri, symbols ?? []]
+		}),
+	)
+	return Object.fromEntries(filesAndSymbols)
+}
+
+// AIDIFF: Added getAst and getTreePathAtCursor from continue/core/autocomplete/util/ast.ts
+// PLANREF: continue/core/autocomplete/util/ast.ts
+
+export async function getAst(filepath: string, fileContents: string): Promise<Tree | undefined> {
+	const parser = await getParserForFile(filepath)
+
+	if (!parser) {
+		return undefined
+	}
+
+	try {
+		const ast = parser.parse(fileContents)
+		return ast || undefined
+	} catch (e) {
+		// console.warn(`[treeSitter] Error parsing AST for ${filepath}:`, e);
+		return undefined
+	}
+}
+
+export async function getTreePathAtCursor(ast: Tree, cursorIndex: number): Promise<SyntaxNode[]> {
+	const path = [ast.rootNode]
+	while (path[path.length - 1].childCount > 0) {
+		let foundChild = false
+		for (const child of path[path.length - 1].children) {
+			if (child && child.startIndex <= cursorIndex && child.endIndex >= cursorIndex) {
+				path.push(child)
+				foundChild = true
+				break
+			}
+		}
+
+		if (!foundChild) {
+			break
+		}
+	}
+
+	return path
+}
diff --git a/src/shared/AutocompleteSettings.ts b/src/shared/AutocompleteSettings.ts
new file mode 100644
index ********..e6953ce2
--- /dev/null
+++ b/src/shared/AutocompleteSettings.ts
@@ -0,0 +1,159 @@
+/**
+ * Autocomplete provider types
+ */
+export type AutocompleteProvider = "openai" | "fim"
+
+/**
+ * Autocomplete configuration settings for QAX Complete
+ */
+export interface AutocompleteSettings {
+	/**
+	 * Whether autocomplete is enabled
+	 */
+	enabled: boolean
+
+	/**
+	 * Provider type for autocomplete service
+	 */
+	provider?: AutocompleteProvider
+
+	/**
+	 * API key for the autocomplete service
+	 */
+	apiKey?: string
+
+	/**
+	 * Base URL for the autocomplete API
+	 */
+	apiBaseUrl?: string
+
+	/**
+	 * Model ID to use for autocomplete
+	 */
+	modelId?: string
+
+	/**
+	 * Maximum number of tokens for completion
+	 */
+	maxTokens?: number
+
+	/**
+	 * Temperature for completion generation
+	 */
+	temperature?: number
+
+	/**
+	 * Request timeout in milliseconds
+	 */
+	requestTimeoutMs?: number
+
+	/**
+	 * Whether to use prompt caching
+	 */
+	usePromptCache?: boolean
+
+	/**
+	 * Custom headers for API requests
+	 */
+	customHeaders?: Record<string, string>
+
+	/**
+	 * Debounce time in milliseconds for autocomplete requests
+	 */
+	debounceMs?: number
+
+	/**
+	 * FIM-specific settings
+	 */
+	fim?: {
+		/**
+		 * API key for FIM service
+		 */
+		apiKey?: string
+
+		/**
+		 * Base URL for FIM API
+		 */
+		baseUrl?: string
+	}
+}
+
+/**
+ * Default autocomplete settings
+ */
+export const DEFAULT_AUTOCOMPLETE_SETTINGS: AutocompleteSettings = {
+	enabled: false,
+	provider: "openai",
+	apiBaseUrl: "https://api.openrouter.ai/api/v1", // Default to OpenRouter, but supports any OpenAI-compatible API
+	modelId: "google/gemini-2.5-flash-preview-05-20",
+	maxTokens: 1000,
+	temperature: 0.1,
+	requestTimeoutMs: 30000,
+	usePromptCache: false,
+	customHeaders: {},
+	debounceMs: 300, // 300ms debounce by default
+	fim: {
+		baseUrl: "",
+		apiKey: "",
+	},
+}
+
+/**
+ * Validates autocomplete settings
+ */
+export function validateAutocompleteSettings(settings: Partial<AutocompleteSettings>): string[] {
+	const errors: string[] = []
+
+	if (settings.enabled) {
+		if (settings.provider === "fim") {
+			// For FIM provider, check FIM-specific settings
+			if (!settings.fim?.apiKey) {
+				errors.push("FIM API key is required when FIM provider is enabled")
+			}
+			if (!settings.fim?.baseUrl) {
+				errors.push("FIM base URL is required when FIM provider is enabled")
+			} else if (!isValidUrl(settings.fim.baseUrl)) {
+				errors.push("Invalid FIM base URL")
+			}
+		} else {
+			// For OpenAI provider, check general API key
+			if (!settings.apiKey) {
+				errors.push("API key is required when autocomplete is enabled")
+			}
+		}
+	}
+
+	if (settings.apiBaseUrl && !isValidUrl(settings.apiBaseUrl)) {
+		errors.push("Invalid API base URL")
+	}
+
+	if (settings.maxTokens && (settings.maxTokens < 1 || settings.maxTokens > 10000)) {
+		errors.push("Max tokens must be between 1 and 10000")
+	}
+
+	if (settings.temperature && (settings.temperature < 0 || settings.temperature > 2)) {
+		errors.push("Temperature must be between 0 and 2")
+	}
+
+	if (settings.requestTimeoutMs && (settings.requestTimeoutMs < 1000 || settings.requestTimeoutMs > 300000)) {
+		errors.push("Request timeout must be between 1000ms and 300000ms")
+	}
+
+	if (settings.debounceMs && (settings.debounceMs < 0 || settings.debounceMs > 5000)) {
+		errors.push("Debounce time must be between 0ms and 5000ms")
+	}
+
+	return errors
+}
+
+/**
+ * Helper function to validate URL
+ */
+function isValidUrl(url: string): boolean {
+	try {
+		new URL(url)
+		return true
+	} catch {
+		return false
+	}
+}
diff --git a/src/shared/ExtensionMessage.ts b/src/shared/ExtensionMessage.ts
index faffa4e5..f3ccd428 100644
--- a/src/shared/ExtensionMessage.ts
+++ b/src/shared/ExtensionMessage.ts
@@ -3,6 +3,7 @@
 import { GitCommit } from "../utils/git"
 import { ApiConfiguration, ModelInfo } from "./api"
 import { AutoApprovalSettings } from "./AutoApprovalSettings"
+import { AutocompleteSettings } from "./AutocompleteSettings"
 import { BrowserSettings } from "./BrowserSettings"
 import { ChatSettings } from "./ChatSettings"
 import { HistoryItem } from "./HistoryItem"
@@ -62,6 +63,7 @@ export interface ExtensionState {
 	isNewUser: boolean
 	apiConfiguration?: ApiConfiguration
 	autoApprovalSettings: AutoApprovalSettings
+	autocompleteSettings: AutocompleteSettings
 	browserSettings: BrowserSettings
 	remoteBrowserHost?: string
 	chatSettings: ChatSettings
diff --git a/src/shared/WebviewMessage.ts b/src/shared/WebviewMessage.ts
index bba92415..3f291066 100644
--- a/src/shared/WebviewMessage.ts
+++ b/src/shared/WebviewMessage.ts
@@ -1,4 +1,5 @@
 import { ApiConfiguration } from "./api"
+import { AutocompleteSettings } from "./AutocompleteSettings"
 import { BrowserSettings } from "./BrowserSettings"
 import { ChatSettings } from "./ChatSettings"
 import { UserInfo } from "./UserInfo"
@@ -12,6 +13,7 @@ export interface WebviewMessage {
 		| "fetchMcpMarketplace"
 		| "searchCommits"
 		| "telemetrySetting"
+		| "updateAutocompleteSettings"
 		| "grpc_request"
 		| "grpc_request_cancel"
 
@@ -22,6 +24,7 @@ export interface WebviewMessage {
 	files?: string[]
 	bool?: boolean
 	number?: number
+	autocompleteSettings?: AutocompleteSettings
 	browserSettings?: BrowserSettings
 	chatSettings?: ChatSettings
 	chatContent?: ChatContent
diff --git a/test-autocomplete-config.js b/test-autocomplete-config.js
new file mode 100644
index ********..17bfbea9
--- /dev/null
+++ b/test-autocomplete-config.js
@@ -0,0 +1,19 @@
+// Simple test to verify autocomplete configuration is working
+// This file can be used to test if the autocomplete status bar appears and configuration works
+
+console.log("Testing autocomplete configuration...")
+
+// Test function to simulate some code that might trigger autocomplete
+function testFunction() {
+	const message = "Hello, world!"
+	console.log(message)
+
+	// Add some more lines to test autocomplete context
+	const numbers = [1, 2, 3, 4, 5]
+	const doubled = numbers.map((n) => n * 2)
+
+	return doubled
+}
+
+// Call the test function
+testFunction()
diff --git a/webview-ui/src/components/settings/AutocompleteSettingsSection.tsx b/webview-ui/src/components/settings/AutocompleteSettingsSection.tsx
new file mode 100644
index ********..174ed52b
--- /dev/null
+++ b/webview-ui/src/components/settings/AutocompleteSettingsSection.tsx
@@ -0,0 +1,253 @@
+import { useExtensionState } from "@/context/ExtensionStateContext"
+import { VSCodeButton, VSCodeCheckbox, VSCodeTextField, VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"
+import { useState, useEffect } from "react"
+import Section from "./Section"
+import SectionHeader from "./SectionHeader"
+import { ApiKeyField } from "./common/ApiKeyField"
+import { BaseUrlField } from "./common/BaseUrlField"
+import { ErrorMessage } from "./common/ErrorMessage"
+import { DropdownContainer } from "./common/ModelSelector"
+
+interface AutocompleteSettingsSectionProps {
+	// No props needed for now
+}
+
+export default function AutocompleteSettingsSection(_props: AutocompleteSettingsSectionProps) {
+	const { autocompleteSettings, setAutocompleteSettings } = useExtensionState()
+	const [localSettings, setLocalSettings] = useState(autocompleteSettings)
+	const [errors, setErrors] = useState<string[]>([])
+
+	// Update local settings when global settings change
+	useEffect(() => {
+		setLocalSettings(autocompleteSettings)
+	}, [autocompleteSettings])
+
+	const handleInputChange = (field: string) => (event: any) => {
+		const value = event.target?.value ?? event.detail?.value ?? event
+
+		if (field.startsWith("fim.")) {
+			const fimField = field.split(".")[1] as keyof NonNullable<typeof localSettings.fim>
+			setLocalSettings((prev) => ({
+				...prev,
+				fim: {
+					...prev.fim,
+					[fimField]: value,
+				},
+			}))
+		} else {
+			setLocalSettings((prev) => ({
+				...prev,
+				[field]: value,
+			}))
+		}
+	}
+
+	const handleCheckboxChange = (field: keyof typeof localSettings) => (event: any) => {
+		const checked = event.target?.checked ?? event.detail?.checked ?? event
+		setLocalSettings((prev) => ({
+			...prev,
+			[field]: checked,
+		}))
+	}
+
+	const handleSave = async () => {
+		// Validate settings
+		const validationErrors: string[] = []
+
+		if (localSettings.enabled && !localSettings.apiKey) {
+			validationErrors.push("API key is required when autocomplete is enabled")
+		}
+
+		if (localSettings.apiBaseUrl && !isValidUrl(localSettings.apiBaseUrl)) {
+			validationErrors.push("Invalid API base URL")
+		}
+
+		if (localSettings.maxTokens && (localSettings.maxTokens < 1 || localSettings.maxTokens > 10000)) {
+			validationErrors.push("Max tokens must be between 1 and 10000")
+		}
+
+		if (localSettings.temperature && (localSettings.temperature < 0 || localSettings.temperature > 2)) {
+			validationErrors.push("Temperature must be between 0 and 2")
+		}
+
+		if (
+			localSettings.requestTimeoutMs &&
+			(localSettings.requestTimeoutMs < 1000 || localSettings.requestTimeoutMs > 300000)
+		) {
+			validationErrors.push("Request timeout must be between 1000ms and 300000ms")
+		}
+
+		setErrors(validationErrors)
+
+		if (validationErrors.length === 0) {
+			setAutocompleteSettings(localSettings)
+		}
+	}
+
+	const handleReset = () => {
+		setLocalSettings(autocompleteSettings)
+		setErrors([])
+	}
+
+	const isValidUrl = (url: string): boolean => {
+		try {
+			new URL(url)
+			return true
+		} catch {
+			return false
+		}
+	}
+
+	const hasChanges = JSON.stringify(localSettings) !== JSON.stringify(autocompleteSettings)
+
+	return (
+		<Section>
+			<SectionHeader description="Configure QAX autocomplete settings for intelligent code completion">
+				QAX Autocomplete Configuration
+			</SectionHeader>
+
+			{errors.length > 0 && (
+				<div style={{ marginBottom: "16px" }}>
+					{errors.map((error, index) => (
+						<ErrorMessage key={index} message={error} />
+					))}
+				</div>
+			)}
+
+			<div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
+				<VSCodeCheckbox checked={localSettings.enabled} onChange={handleCheckboxChange("enabled")}>
+					Enable QAX Autocomplete
+				</VSCodeCheckbox>
+
+				<DropdownContainer className="dropdown-container">
+					<label htmlFor="autocomplete-provider">
+						<span style={{ fontWeight: 500 }}>Provider</span>
+					</label>
+					<VSCodeDropdown
+						id="autocomplete-provider"
+						value={localSettings.provider || "openai"}
+						onChange={handleInputChange("provider")}
+						style={{ width: "100%" }}>
+						<VSCodeOption value="openai">OpenAI Compatible</VSCodeOption>
+						<VSCodeOption value="fim">FIM (Fill in the Middle)</VSCodeOption>
+					</VSCodeDropdown>
+				</DropdownContainer>
+
+				{localSettings.provider === "fim" ? (
+					<>
+						<ApiKeyField
+							value={localSettings.fim?.apiKey || ""}
+							onChange={handleInputChange("fim.apiKey")}
+							providerName="FIM API"
+							placeholder="Enter your FIM API key..."
+						/>
+
+						<BaseUrlField
+							value={localSettings.fim?.baseUrl || ""}
+							onChange={handleInputChange("fim.baseUrl")}
+							placeholder="https://your-fim-api.com/v1"
+							label="FIM Base URL"
+						/>
+					</>
+				) : (
+					<>
+						<ApiKeyField
+							value={localSettings.apiKey || ""}
+							onChange={handleInputChange("apiKey")}
+							providerName="OpenAI Compatible API"
+							placeholder="Enter your API key..."
+						/>
+
+						<BaseUrlField
+							value={localSettings.apiBaseUrl || ""}
+							onChange={handleInputChange("apiBaseUrl")}
+							placeholder="https://api.openrouter.ai/api/v1 (or any OpenAI-compatible API)"
+							label="API Base URL"
+						/>
+					</>
+				)}
+
+				<VSCodeTextField
+					value={localSettings.modelId || ""}
+					style={{ width: "100%" }}
+					onInput={handleInputChange("modelId")}
+					placeholder="google/gemini-2.5-flash-preview-05-20">
+					<span style={{ fontWeight: 500 }}>Model ID</span>
+				</VSCodeTextField>
+
+				<VSCodeTextField
+					value={localSettings.maxTokens?.toString() || ""}
+					style={{ width: "100%" }}
+					onInput={(e) => handleInputChange("maxTokens")(parseInt((e.target as HTMLInputElement)?.value) || 1000)}
+					placeholder="1000">
+					<span style={{ fontWeight: 500 }}>Max Tokens</span>
+				</VSCodeTextField>
+
+				<VSCodeTextField
+					value={localSettings.temperature?.toString() || ""}
+					style={{ width: "100%" }}
+					onInput={(e) => handleInputChange("temperature")(parseFloat((e.target as HTMLInputElement)?.value) || 0.1)}
+					placeholder="0.1">
+					<span style={{ fontWeight: 500 }}>Temperature</span>
+				</VSCodeTextField>
+
+				<VSCodeTextField
+					value={localSettings.requestTimeoutMs?.toString() || ""}
+					style={{ width: "100%" }}
+					onInput={(e) =>
+						handleInputChange("requestTimeoutMs")(parseInt((e.target as HTMLInputElement)?.value) || 30000)
+					}
+					placeholder="30000">
+					<span style={{ fontWeight: 500 }}>Request Timeout (ms)</span>
+				</VSCodeTextField>
+
+				<VSCodeTextField
+					value={localSettings.debounceMs?.toString() || ""}
+					style={{ width: "100%" }}
+					onInput={(e) => handleInputChange("debounceMs")(parseInt((e.target as HTMLInputElement)?.value) || 300)}
+					placeholder="300">
+					<span style={{ fontWeight: 500 }}>Debounce Time (ms)</span>
+				</VSCodeTextField>
+
+				<VSCodeCheckbox checked={localSettings.usePromptCache || false} onChange={handleCheckboxChange("usePromptCache")}>
+					Use Prompt Cache
+				</VSCodeCheckbox>
+
+				{hasChanges && (
+					<div style={{ display: "flex", gap: "8px", marginTop: "16px" }}>
+						<VSCodeButton onClick={handleSave}>Save Settings</VSCodeButton>
+						<VSCodeButton appearance="secondary" onClick={handleReset}>
+							Reset
+						</VSCodeButton>
+					</div>
+				)}
+			</div>
+
+			<div
+				style={{
+					marginTop: "24px",
+					padding: "12px",
+					backgroundColor: "var(--vscode-textBlockQuote-background)",
+					border: "1px solid var(--vscode-textBlockQuote-border)",
+					borderRadius: "4px",
+				}}>
+				<p
+					style={{
+						margin: 0,
+						fontSize: "12px",
+						color: "var(--vscode-descriptionForeground)",
+					}}>
+					<strong>Note:</strong> QAX Autocomplete provides intelligent code completion using AI models. Choose between:
+					<br />
+					<strong>OpenAI Compatible:</strong> Works with OpenRouter, OpenAI, Azure OpenAI, local models (Ollama, LM
+					Studio), and other OpenAI-compatible services.
+					<br />
+					<strong>FIM (Fill in the Middle):</strong> Specialized API format that sends both prefix and suffix context
+					for more accurate completions.
+					<br />
+					Configure your provider settings to enable intelligent code suggestions based on your current context.
+				</p>
+			</div>
+		</Section>
+	)
+}
diff --git a/webview-ui/src/components/settings/SettingsView.tsx b/webview-ui/src/components/settings/SettingsView.tsx
index 066bfe08..2c40cf4d 100644
--- a/webview-ui/src/components/settings/SettingsView.tsx
+++ b/webview-ui/src/components/settings/SettingsView.tsx
@@ -14,6 +14,7 @@ import { useEvent } from "react-use"
 import { Tab, TabContent, TabHeader, TabList, TabTrigger } from "../common/Tab"
 import { TabButton } from "../mcp/configuration/McpConfigurationView"
 import ApiOptions from "./ApiOptions"
+import AutocompleteSettingsSection from "./AutocompleteSettingsSection"
 import BrowserSettingsSection from "./BrowserSettingsSection"
 import { BrowserSettings } from "@shared/BrowserSettings"
 import FeatureSettingsSection from "./FeatureSettingsSection"
@@ -51,6 +52,13 @@ export const SETTINGS_TABS: SettingsTab[] = [
 		headerText: "API Configuration",
 		icon: Webhook,
 	},
+	{
+		id: "autocomplete",
+		name: "Autocomplete",
+		tooltipText: "Autocomplete Settings",
+		headerText: "QAX Autocomplete Settings",
+		icon: FlaskConical,
+	},
 	{
 		id: "general",
 		name: "General",
@@ -833,6 +841,14 @@ const SettingsView = ({ onDone, targetSection }: SettingsViewProps) => {
 								</div>
 							)}
 
+							{/* Autocomplete Settings Tab */}
+							{activeTab === "autocomplete" && (
+								<div>
+									{renderSectionHeader("autocomplete")}
+									<AutocompleteSettingsSection />
+								</div>
+							)}
+
 							{/* General Settings Tab */}
 							{activeTab === "general" && (
 								<div>
diff --git a/webview-ui/src/components/settings/common/BaseUrlField.tsx b/webview-ui/src/components/settings/common/BaseUrlField.tsx
index 5383ff7a..98324296 100644
--- a/webview-ui/src/components/settings/common/BaseUrlField.tsx
+++ b/webview-ui/src/components/settings/common/BaseUrlField.tsx
@@ -1,5 +1,4 @@
-import { useState, useEffect } from "react"
-import { VSCodeCheckbox, VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
+import { VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
 
 /**
  * Props for the BaseUrlField component
@@ -13,45 +12,23 @@ interface BaseUrlFieldProps {
 }
 
 /**
- * A reusable component for toggling and entering custom base URLs
+ * A reusable component for entering base URLs
  */
 export const BaseUrlField = ({
 	value,
 	onChange,
 	defaultValue = "",
-	label = "Use custom base URL",
-	placeholder = "Default: https://api.example.com",
+	label = "Base URL",
+	placeholder = "https://api.example.com",
 }: BaseUrlFieldProps) => {
-	const [isEnabled, setIsEnabled] = useState(!!value)
-
-	// When value changes externally, update isEnabled state
-	useEffect(() => {
-		setIsEnabled(!!value)
-	}, [value])
-
-	const handleToggle = (e: any) => {
-		const checked = e.target.checked === true
-		setIsEnabled(checked)
-		if (!checked) {
-			onChange("")
-		}
-	}
-
 	return (
-		<div>
-			<VSCodeCheckbox checked={isEnabled} onChange={handleToggle}>
-				{label}
-			</VSCodeCheckbox>
-
-			{isEnabled && (
-				<VSCodeTextField
-					value={value || ""}
-					style={{ width: "100%", marginTop: 3 }}
-					type="url"
-					onInput={(e: any) => onChange(e.target.value)}
-					placeholder={placeholder}
-				/>
-			)}
-		</div>
+		<VSCodeTextField
+			value={value || ""}
+			style={{ width: "100%" }}
+			type="url"
+			onInput={(e: any) => onChange(e.target.value)}
+			placeholder={placeholder}>
+			<span style={{ fontWeight: 500 }}>{label}</span>
+		</VSCodeTextField>
 	)
 }
diff --git a/webview-ui/src/context/ExtensionStateContext.tsx b/webview-ui/src/context/ExtensionStateContext.tsx
index c462183d..8286184f 100644
--- a/webview-ui/src/context/ExtensionStateContext.tsx
+++ b/webview-ui/src/context/ExtensionStateContext.tsx
@@ -1,5 +1,6 @@
 import React, { createContext, useCallback, useContext, useEffect, useRef, useState } from "react"
 import { useEvent } from "react-use"
+import { vscode } from "../utils/vscode"
 import {
 	StateServiceClient,
 	ModelsServiceClient,
@@ -14,6 +15,7 @@ import { TerminalProfile } from "@shared/proto/state"
 import { convertProtoToClineMessage } from "@shared/proto-conversions/cline-message"
 import { convertProtoMcpServersToMcpServers } from "@shared/proto-conversions/mcp/mcp-server-conversion"
 import { DEFAULT_AUTO_APPROVAL_SETTINGS } from "@shared/AutoApprovalSettings"
+import { DEFAULT_AUTOCOMPLETE_SETTINGS, AutocompleteSettings } from "@shared/AutocompleteSettings"
 import { DEFAULT_BROWSER_SETTINGS, BrowserSettings } from "@shared/BrowserSettings"
 import { ChatSettings, DEFAULT_CHAT_SETTINGS } from "@shared/ChatSettings"
 import { DEFAULT_PLATFORM, ExtensionMessage, ExtensionState } from "@shared/ExtensionMessage"
@@ -80,6 +82,7 @@ interface ExtensionStateContextType extends ExtensionState {
 	setTotalTasksSize: (value: number | null) => void
 	setAvailableTerminalProfiles: (profiles: TerminalProfile[]) => void // Setter for profiles
 	setBrowserSettings: (value: BrowserSettings) => void
+	setAutocompleteSettings: (value: AutocompleteSettings) => void
 
 	// Refresh functions
 	refreshOpenRouterModels: () => void
@@ -183,6 +186,7 @@ export const ExtensionStateContextProvider: React.FC<{
 		taskHistory: [],
 		shouldShowAnnouncement: false,
 		autoApprovalSettings: DEFAULT_AUTO_APPROVAL_SETTINGS,
+		autocompleteSettings: DEFAULT_AUTOCOMPLETE_SETTINGS,
 		browserSettings: DEFAULT_BROWSER_SETTINGS,
 		chatSettings: DEFAULT_CHAT_SETTINGS,
 		platform: DEFAULT_PLATFORM,
@@ -844,6 +848,17 @@ export const ExtensionStateContextProvider: React.FC<{
 				...prevState,
 				browserSettings: value,
 			})),
+		setAutocompleteSettings: (value: AutocompleteSettings) => {
+			setState((prevState) => ({
+				...prevState,
+				autocompleteSettings: value,
+			}))
+			// Send message to extension to update autocomplete settings
+			vscode.postMessage({
+				type: "updateAutocompleteSettings",
+				autocompleteSettings: value,
+			})
+		},
 	}
 
 	return <ExtensionStateContext.Provider value={contextValue}>{children}</ExtensionStateContext.Provider>
diff --git a/webview-ui/src/utils/context-mentions.ts b/webview-ui/src/utils/context-mentions.ts
index 88e4f7c7..4cd5f449 100644
--- a/webview-ui/src/utils/context-mentions.ts
+++ b/webview-ui/src/utils/context-mentions.ts
@@ -1,6 +1,10 @@
 import { mentionRegex } from "@shared/context-mentions"
 import { Fzf } from "fzf"
-import * as path from "path"
+
+// Browser-compatible basename function
+function basename(filePath: string): string {
+	return filePath.split("/").pop() || filePath.split("\\").pop() || filePath
+}
 
 export interface SearchResult {
 	path: string
@@ -199,7 +203,7 @@ export function getContextMenuOptions(
 		const item = {
 			type: result.type === "folder" ? ContextMenuOptionType.Folder : ContextMenuOptionType.File,
 			value: formattedPath,
-			label: result.label || path.basename(result.path),
+			label: result.label || basename(result.path),
 			description: formattedPath,
 		}
 		return item
